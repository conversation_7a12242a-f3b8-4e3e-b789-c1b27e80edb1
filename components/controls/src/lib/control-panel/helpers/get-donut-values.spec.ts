import { describe, expect, test } from 'vitest';
import {
    criticalBackgroundStrongActive,
    neutralBackgroundModerate,
    successBackgroundModerate,
} from '@cosmos/constants/tokens';
import type { StatsBlockProps } from '../../types/stats-block.types';
import { getDonutValues } from './get-donut-values';

describe('getDonutValues', () => {
    test('should return correct values for legend values array', () => {
        const legendValues = [
            {
                label: 'Ready',
                value: 5,
                color: successBackgroundModerate,
            },
            {
                label: 'Not ready',
                value: 3,
                color: criticalBackgroundStrongActive,
            },
        ];

        const result = getDonutValues(legendValues);

        expect(result).toStrictEqual([
            {
                label: 'Ready',
                value: 5,
                color: successBackgroundModerate,
            },
            {
                label: 'Not ready',
                value: 3,
                color: criticalBackgroundStrongActive,
            },
        ]);
    });

    test('should handle three-value legend array', () => {
        const legendValues = [
            {
                label: 'Ready',
                value: 4,
                color: successBackgroundModerate,
            },
            {
                label: 'Not ready',
                value: 2,
                color: criticalBackgroundStrongActive,
            },
            {
                label: 'Not tested',
                value: 2,
                color: neutralBackgroundModerate,
            },
        ];

        const result = getDonutValues(legendValues);

        expect(result).toStrictEqual([
            {
                label: 'Ready',
                value: 4,
                color: successBackgroundModerate,
            },
            {
                label: 'Not ready',
                value: 2,
                color: criticalBackgroundStrongActive,
            },
            {
                label: 'Not tested',
                value: 2,
                color: neutralBackgroundModerate,
            },
        ]);
    });

    test('should handle empty array', () => {
        const legendValues: StatsBlockProps['legendValues'] = [];

        const result = getDonutValues(legendValues);

        expect(result).toStrictEqual([]);
    });

    test('should handle zero values correctly', () => {
        const legendValues = [
            {
                label: 'Ready',
                value: 0,
                color: successBackgroundModerate,
            },
            {
                label: 'Not ready',
                value: 0,
                color: criticalBackgroundStrongActive,
            },
        ];

        const result = getDonutValues(legendValues);

        expect(result).toStrictEqual([
            {
                label: 'Ready',
                value: 0,
                color: successBackgroundModerate,
            },
            {
                label: 'Not ready',
                value: 0,
                color: criticalBackgroundStrongActive,
            },
        ]);
    });
});
