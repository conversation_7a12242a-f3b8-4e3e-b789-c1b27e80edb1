import type { EmptyStateProps } from '@cosmos/components/empty-state';
import type { IconProps } from '@cosmos/components/icon';
import type { DataLegendProps } from '@cosmos-lab/components/data-legend';

export interface StatsBlockProps {
    'data-id': string;
    'data-testid': string;
    title: string;
    isLoading: boolean;
    iconName?: IconProps['name'];
    iconColor: IconProps['colorScheme'];
    statusLabel: string;
    legendValues: DataLegendProps['data'];
    emptyStateProps: EmptyStateProps;
}
