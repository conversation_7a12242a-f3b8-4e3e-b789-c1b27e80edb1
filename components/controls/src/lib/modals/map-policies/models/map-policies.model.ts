import { isEmpty, isNil, noop } from 'lodash-es';
import { z } from 'zod';
import {
    sharedControlLinkedWorkspacesController,
    sharedControlsMapPolicyMutationController,
} from '@controllers/controls';
import {
    type PolicyItem,
    sharedPoliciesInfiniteListController,
} from '@controllers/policies';
import type { ButtonProps } from '@cosmos/components/button';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { FormSchema, FormValues } from '@ui/forms';
import { getLinkedWorkspacesField } from '../../../fields/linked-workspaces-custom-field/get-linked-workspaces-field.helper';
import { PoliciesComboboxField } from '../components/polices-combobox-field.component';
import { closeMapPoliciesModal } from '../helpers/close-map-policies-modal.helper';
import type { MapPoliciesModalProps } from '../types/map-policies-modal-props.type';

interface MapPoliciesFormValues {
    policies: PolicyItem[];
    linkedWorkspaces?: ListBoxItemData[];
}

class MapEvidenceModalModel {
    countSelectedPolicies = 0;
    triggerSubmit: () => Promise<boolean> | undefined = () => undefined;
    props: MapPoliciesModalProps = {
        controlId: undefined,
        onConfirm: noop,
    };

    constructor() {
        makeAutoObservable(this);
    }

    get isControlIdProvided(): boolean {
        return this.props.controlId !== undefined;
    }

    get hasControlLinkedWorkspacesGroup() {
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;

        return !isNil(controlLinkedWorkspacesGroup?.id);
    }

    get schema(): FormSchema {
        const {
            policiesOptions,
            hasNextPage,
            isFetching,
            isLoading,
            loadNextPage,
        } = sharedPoliciesInfiniteListController;

        return {
            policies: {
                type: 'custom',
                render: PoliciesComboboxField,
                validateWithDefault: 'combobox',
                label: t`Search policies`,
                loaderLabel: t`Loading results`,
                removeAllSelectedItemsLabel: t`Clear all`,
                getSearchEmptyState: () =>
                    t`No policies found. Try adjusting your search.`,
                isMultiSelect: true,
                options: policiesOptions,
                hasMore: hasNextPage,
                isLoading: isFetching && isLoading,
                onFetchOptions: loadNextPage,
                placeholder: t`Search by policy name...`,
                isOptional: !this.isControlIdProvided,
                validator: z.array(z.object({ value: z.string() })).min(1, {
                    message: t`Choose at least one policy`,
                }),
            },
            ...(this.hasControlLinkedWorkspacesGroup && {
                linkedWorkspaces: getLinkedWorkspacesField(),
            }),
        };
    }

    get rightActionStack(): ButtonProps[] {
        return [
            {
                label: t`Cancel`,
                level: 'secondary',
                cosmosUseWithCaution_isDisabled: this.isMappingPolicies,
                onClick: closeMapPoliciesModal,
            },
            {
                label: t`Save`,
                level: 'primary',
                colorScheme: 'primary',
                type: 'submit',
                isLoading: this.isMappingPolicies,
                onClick: action(() => {
                    this.triggerSubmit()?.catch(() => {
                        logger.error('Failed to submit form');
                    });
                }),
            },
        ];
    }

    get shouldShowCreateButton(): boolean {
        const { hasPolicyManagePermission } = sharedFeatureAccessModel;

        return this.isControlIdProvided && hasPolicyManagePermission;
    }

    handleSubmit = (values: FormValues): void => {
        const typedValues = values as unknown as MapPoliciesFormValues;

        if (!this.isControlIdProvided) {
            this.props.onConfirm?.(
                typedValues.policies.map((item) => item.policyData),
            );
            closeMapPoliciesModal();

            return;
        }

        this.handleMapPoliciesToControl(typedValues);
    };

    handleMapPoliciesToControl = (formValues: MapPoliciesFormValues): void => {
        const { policies } = formValues;

        const unpublishedPolicies = policies.filter(
            ({ policyData }) =>
                !policyData.versions.some(
                    (version) => version.isCurrentPublished,
                ),
        );

        if (isEmpty(unpublishedPolicies)) {
            this.mapPoliciesToControl(formValues);
        } else {
            openConfirmationModal({
                title: t`Map unpublished policy`,
                body: t`You are mapping an unpublished policy to this control. This will make your control not ready`,
                confirmText: t`Confirm`,
                cancelText: t`Close`,
                type: 'danger',
                isLoading: () => this.isMappingPolicies,
                onConfirm: action(() => {
                    this.mapPoliciesToControl(formValues);
                }),
                onCancel: closeConfirmationModal,
            });
        }
    };

    mapPoliciesToControl = (formValues: MapPoliciesFormValues): void => {
        const { controlId } = this.props;
        const { linkedWorkspaces, policies } = formValues;
        const { currentWorkspace } = sharedWorkspacesController;

        if (!controlId || !currentWorkspace) {
            return;
        }

        const body: {
            policyIds: number[];
            linkedWorkspaceIds?: number[];
        } = {
            policyIds: policies.map((item) => item.policyData.id),
        };

        if (this.hasControlLinkedWorkspacesGroup) {
            const selectedWorkspaceIds =
                linkedWorkspaces?.map((item) => Number(item.id)) ?? [];

            body.linkedWorkspaceIds = [
                ...selectedWorkspaceIds,
                currentWorkspace.id, // need to add current workspace always
            ];
        }

        sharedControlsMapPolicyMutationController.mapControlPolicies(
            controlId,
            {
                ...body,
                updateDisabledControls: false,
            },
        );

        when(
            () => !this.isMappingPolicies,
            () => {
                closeConfirmationModal();
                closeMapPoliciesModal();
                sharedPoliciesInfiniteListController.removeQuery();
            },
        );
    };

    get isMappingPolicies(): boolean {
        return sharedControlsMapPolicyMutationController.isMapping;
    }

    load = (): void => {
        const { controlId } = this.props;

        const query = controlId
            ? {
                  excludeControlId: controlId,
                  onlyCurrentPublishedVersion: false,
              }
            : {};

        sharedPoliciesInfiniteListController.load(query);
    };
}

export const mapPoliciesModalModel = new MapEvidenceModalModel();
