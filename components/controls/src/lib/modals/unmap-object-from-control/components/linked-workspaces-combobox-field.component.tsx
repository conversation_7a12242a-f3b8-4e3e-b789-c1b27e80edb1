import { useEffect } from 'react';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { runInAction } from '@globals/mobx';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useUniversalFieldController,
} from '@ui/forms';
import { unmapObjectFromControlModel } from '../models/unmap-object-from-control.model';

export const LinkedWorkspacesComboboxField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const [linkedWorkspaces] = useUniversalFieldController<'combobox'>(name);

    useEffect(() => {
        runInAction(() => {
            unmapObjectFromControlModel.currentValue =
                linkedWorkspaces.value as ListBoxItemData[];
        });
    }, [linkedWorkspaces.value]);

    return (
        <UniversalFormField
            __fromCustomRender
            formId={formId}
            name={name}
            data-id={dataId}
            data-testid="LinkedWorkspacesComboboxField"
        />
    );
};
