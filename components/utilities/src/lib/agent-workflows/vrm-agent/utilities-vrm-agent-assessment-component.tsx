import type React from 'react';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { observer } from '@globals/mobx';
import { UtilitiesVrmAgentBaseComponent } from './utilities-vrm-agent-base-component';

interface UtilitiesVrmAgentAssessmentComponentProps {
    vendorId?: number;
    'data-id'?: string;
}

export const UtilitiesVrmAgentAssessmentComponent = observer(
    ({
        'data-id': dataId,
    }: UtilitiesVrmAgentAssessmentComponentProps = {}): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;

        // Use messages from controller, fallback to mock data
        const displayMessages = controller.hasWorkflowMessages
            ? controller.workflowMessages
            : [];

        return (
            <UtilitiesVrmAgentBaseComponent
                messages={displayMessages}
                data-id={dataId || 'vrm-agent-assessment'}
            />
        );
    },
);
