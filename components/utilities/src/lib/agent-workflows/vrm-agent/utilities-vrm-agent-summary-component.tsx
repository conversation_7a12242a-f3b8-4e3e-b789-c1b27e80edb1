import type React from 'react';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { observer } from '@globals/mobx';
import { UtilitiesVrmAgentBaseComponent } from './utilities-vrm-agent-base-component';

interface UtilitiesVrmAgentSummaryComponentProps {
    vendorId?: number;
    'data-id'?: string;
}

export const UtilitiesVrmAgentSummaryComponent = observer(
    ({
        'data-id': dataId,
    }: UtilitiesVrmAgentSummaryComponentProps = {}): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;

        const displayMessages = controller.hasWorkflowMessages
            ? controller.workflowMessages
            : [];

        return (
            <UtilitiesVrmAgentBaseComponent
                messages={displayMessages}
                data-id={dataId || 'vrm-agent-summary'}
            />
        );
    },
);
