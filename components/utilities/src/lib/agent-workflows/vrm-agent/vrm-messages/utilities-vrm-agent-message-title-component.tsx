import type React from 'react';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import type { UtilitiesVrmAgentMessageTitleItem } from './types';

export interface UtilitiesVrmAgentMessageTitleProps {
    items: UtilitiesVrmAgentMessageTitleItem[];
    'data-id'?: string;
}

export const UtilitiesVrmAgentMessageTitleComponent = observer(
    ({
        items,
        'data-id': dataId,
    }: UtilitiesVrmAgentMessageTitleProps): React.JSX.Element => {
        return (
            <Stack
                direction="row"
                gap="sm"
                align="center"
                data-testid="UtilitiesVrmAgentMessageTitleComponent"
                data-id={dataId || 'agent-message-title'}
            >
                {items.map((item, index) => (
                    <Stack
                        key={item.id}
                        direction="row"
                        gap="xs"
                        align="center"
                        data-id="PRaSUCNo"
                    >
                        {item.icon && (
                            <Icon
                                name={item.icon}
                                size="200"
                                colorScheme="success"
                                data-id={`${dataId || 'agent-message-title'}-icon-${index}`}
                            />
                        )}
                        {item.text && (
                            <>
                                {item.ref ? (
                                    <AppLink
                                        href={item.ref}
                                        colorScheme="primary"
                                        size="md"
                                        data-id={`${dataId || 'agent-message-title'}-link-${index}`}
                                    >
                                        {item.text}
                                    </AppLink>
                                ) : (
                                    <Text
                                        allowBold
                                        size="200"
                                        type="title"
                                        data-id={`${dataId || 'agent-message-title'}-text-${index}`}
                                    >
                                        <strong>{item.text}</strong>
                                    </Text>
                                )}
                            </>
                        )}
                    </Stack>
                ))}
            </Stack>
        );
    },
);
