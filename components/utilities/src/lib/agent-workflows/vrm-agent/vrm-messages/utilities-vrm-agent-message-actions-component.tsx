import type React from 'react';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { observer, runInAction } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import type { UtilitiesVrmAgentMessageAction } from './types';

export interface UtilitiesVrmAgentMessageActionsProps {
    actions: UtilitiesVrmAgentMessageAction[];
    'data-id'?: string;
}

export const UtilitiesVrmAgentMessageActionsComponent = observer(
    ({
        actions,
        'data-id': dataId,
    }: UtilitiesVrmAgentMessageActionsProps): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;

        const handleActionClick = (
            action: UtilitiesVrmAgentMessageAction,
        ): void => {
            runInAction(() => {
                controller.handleActionClick(action);
            });
        };

        return (
            <Stack
                direction="column"
                align="start"
                data-testid="UtilitiesVrmAgentMessageActions"
                data-id={dataId || 'agent-message-actions'}
            >
                {actions.map((action, index) => {
                    switch (action.type) {
                        case 'link': {
                            return (
                                <Box
                                    key={action.id}
                                    pb="1x"
                                    data-id={`${dataId || 'agent-message-actions'}-link-box-${index}`}
                                >
                                    <AppLink
                                        href={action.action}
                                        colorScheme="primary"
                                        size="sm"
                                        data-id={`${dataId || 'agent-message-actions'}-link-${index}`}
                                    >
                                        {action.text}
                                    </AppLink>
                                </Box>
                            );
                        }
                        case 'action': {
                            return (
                                <Button
                                    hasPadding={false}
                                    key={action.id}
                                    label={action.text}
                                    colorScheme="primary"
                                    level="tertiary"
                                    size="sm"
                                    width="auto"
                                    isLoading={controller.isExecutingAction}
                                    a11yLoadingLabel="Processing action..."
                                    data-id={`${dataId || 'agent-message-actions'}-button-${index}`}
                                    onClick={() => {
                                        handleActionClick(action);
                                    }}
                                />
                            );
                        }
                        default: {
                            return null;
                        }
                    }
                })}
            </Stack>
        );
    },
);
