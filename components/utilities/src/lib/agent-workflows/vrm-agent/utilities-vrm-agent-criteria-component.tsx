import type React from 'react';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { observer } from '@globals/mobx';
import { UtilitiesVrmAgentBaseComponent } from './utilities-vrm-agent-base-component';

interface UtilitiesVrmAgentCriteriaComponentProps {
    vendorId?: number;
    'data-id'?: string;
}

export const UtilitiesVrmAgentCriteriaComponent = observer(
    ({
        'data-id': dataId,
    }: UtilitiesVrmAgentCriteriaComponentProps = {}): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;

        // Use messages from controller, fallback to mock data
        const displayMessages = controller.hasWorkflowMessages
            ? controller.workflowMessages
            : [];

        return (
            <UtilitiesVrmAgentBaseComponent
                messages={displayMessages}
                data-id={dataId || 'vrm-agent-criteria'}
            />
        );
    },
);
