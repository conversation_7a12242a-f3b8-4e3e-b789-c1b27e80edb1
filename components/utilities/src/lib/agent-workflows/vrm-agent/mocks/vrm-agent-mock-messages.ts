import type { UtilitiesVrmAgentMessageData } from '../vrm-messages/types';

/**
 * Mock data simulating a chat conversation between user and agent for VRM Agent showcase.
 */
export const VRM_AGENT_MOCK_MESSAGES: UtilitiesVrmAgentMessageData[] = [
    {
        id: 'vrm-msg-001',
        caller: 'USER',
        title: null,
        body: [],
        actions: [
            {
                id: 'vrm-action-001',
                type: 'pressedButton',
                text: 'Send questionnaire',
                action: 'VendorAssessmentSendQuestionnaire',
            },
        ],
    },
    {
        id: 'vrm-msg-002',
        caller: 'AGENT',
        title: null,
        body: [
            {
                id: 'vrm-body-001',
                text: "I'll help you assess Amazon Web Services. Let me analyze the documentation you've provided and run it against your security criteria.",
            },
        ],
        actions: [],
    },
    {
        id: 'vrm-msg-003',
        caller: 'AGENT',
        title: [
            {
                id: 'vrm-title-001-icon',
                icon: 'Check',
            },
            {
                id: 'vrm-title-001-text',
                text: 'Initial assessment completed',
                ref: 'VendorDocuments',
            },
        ],
        body: [
            {
                id: 'vrm-body-002',
                text: '15 documents analyzed against 17 security criteria',
            },
            {
                id: 'vrm-body-003',
                text: 'Overall security posture: Strong with some gaps',
            },
            {
                id: 'vrm-body-004',
                text: 'Criteria met: 12 | Not met: 3 | Inconclusive: 2',
            },
        ],
        actions: [],
    },
    {
        id: 'vrm-msg-004',
        caller: 'USER',
        title: null,
        body: [],
        actions: [
            {
                id: 'vrm-action-002',
                type: 'pressedButton',
                text: 'Send questionnaire',
                action: 'VendorAssessmentSendQuestionnaire',
            },
        ],
    },
    {
        id: 'vrm-msg-005',
        caller: 'AGENT',
        title: [
            {
                id: 'vrm-title-005-icon',
                icon: 'WarningTriangle',
            },
            {
                id: 'vrm-title-005-text',
                text: 'Security gaps identified',
            },
        ],
        body: [
            {
                id: 'vrm-body-005',
                text: 'The main gaps are in disclosure practices and data handling transparency:',
                style: 'bold',
            },
            {
                id: 'vrm-body-006',
                text: '• Subprocess transparency - Limited information about third-party processors',
            },
            {
                id: 'vrm-body-007',
                text: '• Breach notification protocols - Unclear timeline commitments',
            },
            {
                id: 'vrm-body-008',
                text: '• Encryption details - Some technical specifications lack sufficient detail',
            },
        ],
        actions: [
            {
                id: 'vrm-action-003',
                type: 'button',
                text: 'View detailed report',
                action: 'ViewDetailedReport',
            },
        ],
    },
    {
        id: 'vrm-msg-006',
        caller: 'AGENT',
        title: [
            {
                id: 'vrm-title-006-text',
                text: 'Follow-up questionnaire ready',
            },
        ],
        body: [
            {
                id: 'vrm-body-009',
                text: "I've prepared a follow-up questionnaire to get clarification on these gaps. This will help us complete the assessment.",
            },
        ],
        actions: [
            {
                id: 'vrm-action-006',
                type: 'link',
                text: 'Review questionnaire',
                action: 'VendorsDocuments',
            },
        ],
    },
    {
        id: 'vrm-msg-007',
        caller: 'USER',
        title: null,
        body: [],
        actions: [
            {
                id: 'vrm-action-007',
                type: 'button',
                text: 'Send questionnaire',
                action: 'VendorAssessmentSendQuestionnaire',
            },
        ],
    },
];
