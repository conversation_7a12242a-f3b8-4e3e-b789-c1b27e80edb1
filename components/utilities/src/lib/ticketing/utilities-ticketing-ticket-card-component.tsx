import jiraLogo from '@assets/img/company-logos/jira/jira.svg';
import { sharedControlTicketsController } from '@controllers/controls';
import { sharedTicketDownloadController } from '@controllers/ticket-download';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { DateTime } from '@cosmos-lab/components/date-time';
import { Organization } from '@cosmos-lab/components/organization';
import type { TicketResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { generateFallbackText } from '@helpers/formatters';

interface UtilitiesTicketingTicketCardComponentProps {
    ticket: TicketResponseDto;
    isDeletingTicket: boolean;
    onDeleteTicket: (ticketId: number) => void;
    ['data-id']?: string;
}

const getCreatedByInfo = (ticket: TicketResponseDto) => {
    const createdBy = ticket.createdBy || t`Unknown`;
    const { createdAt } = ticket;

    if (createdAt) {
        // Format the date using table format for consistency
        const formattedDate = formatDate('table', createdAt);

        return t`${createdBy} on ${formattedDate}`;
    }

    return createdBy;
};

export const UtilitiesTicketingTicketCardComponent = observer(
    ({
        ticket,
        isDeletingTicket,
        onDeleteTicket,
        'data-id': dataId,
    }: UtilitiesTicketingTicketCardComponentProps) => {
        const { userHasPermissionToCreateTicket } =
            sharedControlTicketsController;

        return (
            <Box
                key={ticket.id}
                data-id={dataId || 'utilities-ticketing-accordion'}
                p="xl"
                borderRadius="borderRadiusLg"
                borderWidth="borderWidth1"
                borderColor="neutralBorderFaded"
                backgroundColor="neutralBackgroundSurfaceInitial"
            >
                <Stack gap="xl" direction="column">
                    <Stack direction="column" gap="lg">
                        <Text size="200" type="title" colorScheme="neutral">
                            {ticket.title}
                        </Text>
                        <Stack gap="md" direction="row" align="center">
                            <Metadata
                                label="Jira"
                                type="tag"
                                colorScheme="neutral"
                            />
                            <Stack gap="sm" direction="row" align="center">
                                <Organization
                                    fallbackText={'jira'}
                                    imgAlt="jira"
                                    imgSrc={jiraLogo}
                                    size="sm"
                                />
                                <Text
                                    size="100"
                                    type="body"
                                    colorScheme="neutral"
                                >
                                    {ticket.ticketNumber}
                                </Text>
                            </Stack>
                        </Stack>
                    </Stack>
                    <Stack direction="column" gap="xl">
                        <Grid columns="2" gap="2x">
                            <KeyValuePair
                                label={t`Status`}
                                value={ticket.status}
                            />
                            <KeyValuePair
                                label={t`Assignee`}
                                value={ticket.assignee || 'Unassigned'}
                            />
                            <KeyValuePair
                                label={t`Last updated`}
                                type="REACT_NODE"
                                value={
                                    <DateTime
                                        format="table"
                                        date={ticket.updatedAt}
                                    />
                                }
                            />
                            <KeyValuePair
                                label={t`Created by`}
                                type="USER"
                                value={[
                                    {
                                        username: getCreatedByInfo(ticket),
                                        avatarProps: {
                                            fallbackText:
                                                generateFallbackText(
                                                    ticket.createdBy,
                                                ) || '',
                                            imgSrc: '',
                                            imgAlt: ticket.createdBy,
                                        },
                                    },
                                ]}
                            />
                        </Grid>
                    </Stack>

                    <Stack gap="lg" direction="row" align="center">
                        <Button
                            label={t`Manage ticket`}
                            level="secondary"
                            colorScheme="primary"
                            size="sm"
                            endIconName="LinkOut"
                            href={ticket.url}
                            target="_blank"
                        />
                        <Tooltip isInteractive text={t`Download`}>
                            <Button
                                isIconOnly
                                label={t`Download`}
                                level="tertiary"
                                colorScheme="neutral"
                                size="sm"
                                startIconName="Download"
                                a11yLoadingLabel={t`Downloading...`}
                                isLoading={
                                    sharedTicketDownloadController.isLoading
                                }
                                onClick={() => {
                                    sharedTicketDownloadController.downloadTicketFiles(
                                        ticket.id,
                                    );
                                }}
                            />
                        </Tooltip>
                        {userHasPermissionToCreateTicket && (
                            <Tooltip isInteractive text={t`Delete`}>
                                <Button
                                    isIconOnly
                                    label={t`Delete`}
                                    level="tertiary"
                                    colorScheme="neutral"
                                    size="sm"
                                    startIconName="Trash"
                                    cosmosUseWithCaution_isDisabled={
                                        isDeletingTicket
                                    }
                                    onClick={() => {
                                        onDeleteTicket(ticket.id);
                                    }}
                                />
                            </Tooltip>
                        )}
                    </Stack>
                </Stack>
            </Box>
        );
    },
);
