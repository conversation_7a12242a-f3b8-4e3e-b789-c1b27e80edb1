import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
    VendorsProfileReportsAndDocumentsMutationController,
} from '@controllers/vendors';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import {
    vendorsSecurityReviewsControllerCreateSecurityReviewDocumentMutation,
    vendorsSecurityReviewsControllerDeleteSecurityReviewDocumentMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import {
    getNewFiles,
    getRemovedFiles,
} from '../helpers/bridge-letter-file-comparison.helper';

class SOCBridgeLetterMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    uploadBridgeLetterMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerCreateSecurityReviewDocumentMutation,
        {
            onSuccess: () => {
                sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();
            },
        },
    );

    deleteBridgeLetterMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerDeleteSecurityReviewDocumentMutation,
        {
            onSuccess: () => {
                sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();
            },
        },
    );

    get isUploading(): boolean {
        return this.uploadBridgeLetterMutation.isPending;
    }

    get isDeleting(): boolean {
        return this.deleteBridgeLetterMutation.isPending;
    }

    get isBusy(): boolean {
        return this.isUploading || this.isDeleting;
    }

    handleBridgeLetterChanges = (
        currentFiles: File[],
        previousFiles: CosmosFileObject[],
    ): void => {
        const newFiles = getNewFiles(currentFiles, previousFiles);
        const removedFiles = getRemovedFiles(currentFiles, previousFiles);

        if (!isEmpty(newFiles)) {
            this.uploadBridgeLetterFiles(newFiles);
        }

        if (!isEmpty(removedFiles)) {
            this.deleteBridgeLetterFiles(removedFiles);
        }
    };

    uploadNewDocument = (
        file: File,
        documentsController: VendorsProfileReportsAndDocumentsMutationController,
    ): void => {
        const { vendorId } = sharedVendorsSecurityReviewDocumentsController;

        if (!vendorId) {
            return;
        }

        documentsController.uploadFileQuery.mutate({
            body: {
                file,
                type: 'BRIDGE_LETTER',
            },
            path: { id: vendorId },
        });
    };

    uploadBridgeLetterFiles = (files: File[]): void => {
        const securityReviewId =
            sharedVendorsSecurityReviewDetailsController.securityReviewDetails
                ?.id;

        if (!securityReviewId) {
            return;
        }

        files.forEach((fileObject) => {
            const documentsController =
                new VendorsProfileReportsAndDocumentsMutationController();

            this.uploadNewDocument(fileObject, documentsController);

            const { name: fileName } = fileObject;

            when(
                () => !documentsController.uploadFileQuery.isPending,
                () => {
                    if (documentsController.uploadFileQuery.hasError) {
                        snackbarController.addSnackbar({
                            id: `vendor-document-upload-error-${fileName}`,
                            props: {
                                title: t`Failed to upload vendor document ${fileName}`,
                                description: t`Please try again later.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return;
                    }

                    if (
                        Array.isArray(
                            documentsController.uploadFileQuery.response
                                ?.documents,
                        )
                    ) {
                        const bridgeLetterDoc =
                            documentsController.uploadFileQuery.response.documents.find(
                                (doc) => doc.type === 'BRIDGE_LETTER',
                            );

                        if (!bridgeLetterDoc?.id) {
                            return;
                        }

                        const { id, name } = bridgeLetterDoc;

                        this.uploadBridgeLetterMutation.mutate({
                            path: { id: securityReviewId },
                            body: {
                                documentId: id,
                                type: 'BRIDGE_LETTER',
                            },
                        });

                        when(
                            () => !this.uploadBridgeLetterMutation.isPending,
                            () => {
                                if (this.uploadBridgeLetterMutation.hasError) {
                                    snackbarController.addSnackbar({
                                        id: `bridge-letter-upload-error-${name}`,
                                        props: {
                                            title: t`Failed to upload bridge letter ${name}`,
                                            description: t`Please try again later.`,
                                            severity: 'critical',
                                            closeButtonAriaLabel: t`Close`,
                                        },
                                    });
                                }
                            },
                        );
                    }
                },
            );
        });
    };

    deleteBridgeLetterFiles = (files: File[]): void => {
        const { bridgeLetterDocuments } =
            sharedVendorsSecurityReviewDocumentsController;

        if (isEmpty(bridgeLetterDocuments)) {
            return;
        }

        const { vendorId } = sharedVendorsSecurityReviewDocumentsController;

        if (!vendorId) {
            return;
        }

        files.forEach((fileObject) => {
            const documentToDelete = bridgeLetterDocuments.find(
                (doc) => doc.name === fileObject.name,
            );

            if (documentToDelete?.id) {
                const documentsController =
                    new VendorsProfileReportsAndDocumentsMutationController();

                this.deleteBridgeLetterMutation.mutate({
                    path: { id: Number(documentToDelete.id) },
                });

                const { name, id } = documentToDelete;

                when(
                    () => !this.deleteBridgeLetterMutation.isPending,
                    () => {
                        if (this.deleteBridgeLetterMutation.hasError) {
                            snackbarController.addSnackbar({
                                id: 'bridge-letter-delete-error',
                                props: {
                                    title: t`Failed to delete bridge letter ${name}`,
                                    description: t`Please try again later.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        documentsController.deleteFileQuery.mutate({
                            path: {
                                id: vendorId,
                                docId: id,
                            },
                        });
                    },
                );

                when(
                    () => !documentsController.deleteFileQuery.isPending,
                    () => {
                        if (documentsController.deleteFileQuery.hasError) {
                            snackbarController.addSnackbar({
                                id: 'vendor-document-delete-error',
                                props: {
                                    title: t`Failed to delete vendor document ${name}`,
                                    description: t`Please try again later.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        }
                    },
                );
            }
        });
    };
}

export const sharedSOCBridgeLetterMutationController =
    new SOCBridgeLetterMutationController();
