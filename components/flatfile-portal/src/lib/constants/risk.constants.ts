import { RiskStatusType, RiskTreatmentPlan } from '@drata/enums';
import type { RiskRequestDto } from '@globals/api-sdk/types';

// Extract property names as a union type
export type RiskRequestDtoKeys = keyof RiskRequestDto;
export const RISK_KEYS: RiskRequestDtoKeys[] = [
    'description',
    'title',
    'treatmentPlan',
    'treatmentDetails',
    'status',
    'notes',
    'completionDate',
    'anticipatedCompletionDate',
    'identifiedAt',
] as const satisfies string[];

export const RISK_DATE_FIELDS: RiskRequestDtoKeys[] = [
    'completionDate',
    'anticipatedCompletionDate',
    'identifiedAt',
] as const satisfies string[];

export const RISK_DEFAULT_VALUES = {
    status: RiskStatusType.ACTIVE,
    treatmentPlan: RiskTreatmentPlan[RiskTreatmentPlan.UNTREATED],
    owners: [],
};
