import { beforeAll, describe, expect, test, vi } from 'vitest';
import { z } from 'zod';
import { i18n } from '@globals/i18n';
import { buildRiskValidationSchema } from './risk-validations';

const riskValidationSchema = buildRiskValidationSchema();

vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));

vi.mock('@globals/api-sdk/zod', () => {
    const statusEnum = z.enum(['ACTIVE', 'ARCHIVED', 'CLOSED']);

    // Add the Enum property that the schema expects
    Object.defineProperty(statusEnum, 'Enum', {
        value: {
            ACTIVE: 'ACTIVE',
            ARCHIVED: 'ARCHIVED',
            CLOSED: 'CLOSED',
        },
        writable: false,
        enumerable: false,
        configurable: false,
    });

    return {
        zRiskResponseDto: {
            shape: {
                source: z.enum(['DRATA', 'CUSTOM']),
                treatmentPlan: z.enum([
                    'UNTREATED',
                    'ACCEPT',
                    'TRANSFER',
                    'AVOID',
                    'MITIGATE',
                ]),
                status: statusEnum,
                classification: z.enum([
                    'GENERAL_RISK',
                    'FINDINGS',
                    'EXCEPTIONS',
                    'ISSUES',
                    'CONTRACTS',
                    'REGULATIONS',
                ]),
            },
        },
    };
});

describe('riskValidationSchema', () => {
    beforeAll(() => {
        i18n.load('en', {});
        i18n.activate('en');
    });

    describe('title validation', () => {
        test('should accept valid title', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid Risk Title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should accept null title', () => {
            const result = riskValidationSchema.safeParse({
                title: null,
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should accept undefined title', () => {
            const result = riskValidationSchema.safeParse({
                title: undefined,
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should reject title exceeding 191 characters', () => {
            const longTitle = 'a'.repeat(192);
            const result = riskValidationSchema.safeParse({
                title: longTitle,
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeFalsy();
            // Type assertion to access error properties
            const errorResult = result as {
                success: false;
                error: { issues: { message: string }[] };
            };

            expect(errorResult.error.issues[0].message).toBe(
                'Title cannot exceed 191 characters',
            );
        });

        test('should accept title with exactly 191 characters', () => {
            const maxTitle = 'a'.repeat(191);
            const result = riskValidationSchema.safeParse({
                title: maxTitle,
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should reject non-string title', () => {
            const result = riskValidationSchema.safeParse({
                title: 123,
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeFalsy();
        });
    });

    describe('description validation', () => {
        test('should accept valid description', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'This is a valid risk description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should accept null description', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: null,
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should accept undefined description', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: undefined,
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should reject description exceeding 30000 characters', () => {
            const longDescription = 'a'.repeat(30001);
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: longDescription,
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeFalsy();
            // Type assertion to access error properties
            const errorResult = result as {
                success: false;
                error: { issues: { message: string }[] };
            };

            expect(errorResult.error.issues[0].message).toBe(
                'Description cannot exceed 30000 characters',
            );
        });

        test('should accept description with exactly 30000 characters', () => {
            const maxDescription = 'a'.repeat(30000);
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: maxDescription,
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeTruthy();
        });

        test('should reject non-string description', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 123,
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeFalsy();
        });
    });

    describe('source validation', () => {
        test('should accept valid source values', () => {
            const validSources = ['DRATA', 'CUSTOM'];

            for (const source of validSources) {
                const result = riskValidationSchema.safeParse({
                    title: 'Valid title',
                    description: 'Valid description',
                    source,
                    treatmentPlan: 'UNTREATED',
                    status: 'ACTIVE',
                });

                expect(result.success).toBeTruthy();
            }
        });

        test('should reject invalid source values', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'INVALID_SOURCE',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
            });

            expect(result.success).toBeFalsy();
        });
    });

    describe('treatmentPlan validation', () => {
        test('should accept valid treatment plan values', () => {
            const validPlans = [
                'UNTREATED',
                'ACCEPT',
                'TRANSFER',
                'AVOID',
                'MITIGATE',
            ];

            for (const treatmentPlan of validPlans) {
                const result = riskValidationSchema.safeParse({
                    title: 'Valid title',
                    description: 'Valid description',
                    source: 'CUSTOM',
                    treatmentPlan,
                    status: 'ACTIVE',
                });

                expect(result.success).toBeTruthy();
            }
        });

        test('should reject invalid treatment plan values', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'INVALID_PLAN',
                status: 'ACTIVE',
            });

            expect(result.success).toBeFalsy();
        });
    });

    describe('status validation', () => {
        test('should accept valid status values', () => {
            const validStatuses = ['ACTIVE', 'ARCHIVED', 'CLOSED'];

            for (const status of validStatuses) {
                const result = riskValidationSchema.safeParse({
                    title: 'Valid title',
                    description: 'Valid description',
                    source: 'CUSTOM',
                    treatmentPlan: 'UNTREATED',
                    status,
                });

                expect(result.success).toBeTruthy();
            }
        });

        test('should use ACTIVE as default status', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                // status omitted
            });

            expect(result.success).toBeTruthy();
            // Type assertion to access data properties
            const successResult = result as {
                success: true;
                data: { status: string };
            };

            expect(successResult.data.status).toBe('ACTIVE');
        });

        test('should reject invalid status values', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'INVALID_STATUS',
            });

            expect(result.success).toBeFalsy();
        });
    });

    describe('treatmentDetails validation', () => {
        test('should accept valid treatment details', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                treatmentDetails: 'Detailed treatment plan',
            });

            expect(result.success).toBeTruthy();
        });

        test('should accept null treatment details', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                treatmentDetails: null,
            });

            expect(result.success).toBeTruthy();
        });

        test('should reject treatment details exceeding 30000 characters', () => {
            const longTreatmentDetails = 'a'.repeat(30001);
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                treatmentDetails: longTreatmentDetails,
            });

            expect(result.success).toBeFalsy();
            const errorResult = result as {
                success: false;
                error: { issues: { message: string }[] };
            };

            expect(errorResult.error.issues[0].message).toBe(
                'Treatment plan cannot exceed 30000 characters',
            );
        });
    });

    describe('date validation', () => {
        test('should accept valid completion date', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                completionDate: '2024-12-31',
            });

            expect(result.success).toBeTruthy();
        });

        test('should accept null completion date', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                completionDate: null,
            });

            expect(result.success).toBeTruthy();
        });

        test('should reject invalid date format', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                completionDate: 'invalid-date',
            });

            expect(result.success).toBeFalsy();
            const errorResult = result as {
                success: false;
                error: { issues: { message: string }[] };
            };

            expect(errorResult.error.issues[0].message).toBe(
                'Completion date must be a valid date with format yyyy-mm-dd',
            );
        });
    });

    describe('notes validation', () => {
        test('should accept valid notes', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                notes: 'Some internal notes',
            });

            expect(result.success).toBeTruthy();
        });

        test('should accept null notes', () => {
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                notes: null,
            });

            expect(result.success).toBeTruthy();
        });

        test('should reject notes exceeding 768 characters', () => {
            const longNotes = 'a'.repeat(769);
            const result = riskValidationSchema.safeParse({
                title: 'Valid title',
                description: 'Valid description',
                source: 'CUSTOM',
                treatmentPlan: 'UNTREATED',
                status: 'ACTIVE',
                notes: longNotes,
            });

            expect(result.success).toBeFalsy();
            const errorResult = result as {
                success: false;
                error: { issues: { message: string }[] };
            };

            expect(errorResult.error.issues[0].message).toBe(
                'Internal notes cannot exceed 768 characters',
            );
        });
    });

    describe('complete validation', () => {
        test('should validate a complete valid risk object', () => {
            const validRisk = {
                title: 'Data Breach Risk',
                description:
                    'Risk of unauthorized access to sensitive customer data',
                source: 'CUSTOM' as const,
                treatmentPlan: 'MITIGATE' as const,
                status: 'ACTIVE' as const,
                treatmentDetails: 'Implement encryption and access controls',
                completionDate: '2024-12-31',
                notes: 'High priority risk',
                owners: ['<EMAIL>', '<EMAIL>'],
            };

            const result = riskValidationSchema.safeParse(validRisk);

            expect(result.success).toBeTruthy();
            // Type assertion to access data properties
            const successResult = result as unknown as {
                success: true;
                data: typeof validRisk;
            };

            // The owners field should be stripped out since it's not in the schema
            const expectedResult = {
                title: 'Data Breach Risk',
                description:
                    'Risk of unauthorized access to sensitive customer data',
                source: 'CUSTOM' as const,
                treatmentPlan: 'MITIGATE' as const,
                status: 'ACTIVE' as const,
                treatmentDetails: 'Implement encryption and access controls',
                completionDate: '2024-12-31',
                notes: 'High priority risk',
            };

            expect(successResult.data).toStrictEqual(expectedResult);
        });

        test('should validate minimal valid risk object with defaults', () => {
            const minimalRisk = {
                source: 'CUSTOM' as const,
            };

            const result = riskValidationSchema.safeParse(minimalRisk);

            expect(result.success).toBeTruthy();
            // Type assertion to access data properties
            const successResult = result as {
                success: true;
                data: { status: string };
            };

            expect(successResult.data.status).toBe('ACTIVE');
        });
    });
});
