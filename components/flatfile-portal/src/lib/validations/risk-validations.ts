import { z } from 'zod';
import { zRiskResponseDto } from '@globals/api-sdk/zod';
import { t } from '@globals/i18n/macro';

/**
 * Builds the risk validation schema with proper i18n message handling.
 * Using function pattern to avoid race conditions with i18n translations.
 */
export const buildRiskValidationSchema = (): z.ZodObject<z.ZodRawShape> => {
    const validationMessages = {
        title: t`Title cannot exceed 191 characters`,
        description: t`Description cannot exceed 30000 characters`,
        treatmentPlan: t`Treatment plan cannot exceed 30000 characters`,
        date: t`Completion date must be a valid date with format yyyy-mm-dd`,
        notes: t`Internal notes cannot exceed 768 characters`,
    };

    return z.object({
        title: z.string().max(191, validationMessages.title).nullish(),
        description: z
            .string()
            .max(30000, validationMessages.description)
            .nullish(),
        standardCategories: z.string().optional(),
        customCategories: z.array(z.string()).optional(),
        treatmentDetails: z
            .string()
            .max(30000, validationMessages.treatmentPlan)
            .nullish()
            .optional(),
        treatmentPlan: zRiskResponseDto.shape.treatmentPlan.nullish(),
        inherentImpact: z.number().optional(),
        inherentLikelihood: z.number().optional(),
        inherentScore: z.number().optional(),
        residualImpact: z.number().optional(),
        residualLikelihood: z.number().optional(),
        residualScore: z.number().optional(),
        completionDate: z
            .string()
            .date(validationMessages.date)
            .nullish()
            .optional(),
        anticipatedCompletionDate: z
            .string()
            .date(validationMessages.date)
            .nullish()
            .optional(),
        identifiedAt: z
            .string()
            .date(validationMessages.date)
            .nullish()
            .optional(),
        vendor: z.string().optional(),
        source: zRiskResponseDto.shape.source,
        status: zRiskResponseDto.shape.status.default(
            zRiskResponseDto.shape.status.Enum.ACTIVE,
        ),
        notes: z
            .string()
            .max(768, validationMessages.notes)
            .optional()
            .nullable(),
    });
};
