import { beforeEach, describe, expect, test, vi } from 'vitest';
import { z } from 'zod';
import type { FlatfileRecord } from '@flatfile/plugin-record-hook';
import { validateFields } from './validate-fields';

/**
 * Mock FlatfileRecord.
 */
const createMockRecord = (): FlatfileRecord => {
    const values = new Map<string, unknown>();
    const errors = new Map<string, { message: string }[]>();

    return {
        get: vi.fn((key: string) => values.get(key)),
        set: vi.fn((key: string, value: unknown) => {
            values.set(key, value);
        }),
        addError: vi.fn((key: string, message: string) => {
            if (!errors.has(key)) {
                errors.set(key, []);
            }
            errors.get(key)?.push({ message });
        }),
        getErrors: vi.fn((key: string) => errors.get(key) ?? []),
    } as unknown as FlatfileRecord;
};

interface MockedRecord extends FlatfileRecord {
    get: ReturnType<typeof vi.fn>;
    set: ReturnType<typeof vi.fn>;
    addError: ReturnType<typeof vi.fn>;
    getErrors: ReturnType<typeof vi.fn>;
}

// Create a test validation schema
const testValidationSchema = z.object({
    title: z.string().min(1, 'Title is required').max(10, 'Title too long'),
    email: z.string().email('Invalid email format'),
    age: z.number().min(0, 'Age must be positive'),
    status: z.enum(['ACTIVE', 'INACTIVE'], { message: 'Invalid status' }),
});

describe('validateFields', () => {
    beforeEach(() => {
        // Clear the error tracker between tests
        vi.clearAllMocks();
    });

    describe('successful validation', () => {
        test('should not add errors for valid fields', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return 'Valid';
                }
                if (key === 'email') {
                    return '<EMAIL>';
                }

                return undefined;
            });

            validateFields(record, ['title', 'email'], testValidationSchema);

            expect(mockAddError).not.toHaveBeenCalled();
        });

        test('should handle all valid fields', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return 'Valid';
                }
                if (key === 'email') {
                    return '<EMAIL>';
                }
                if (key === 'age') {
                    return 25;
                }
                if (key === 'status') {
                    return 'ACTIVE';
                }

                return undefined;
            });

            validateFields(
                record,
                ['title', 'email', 'age', 'status'],
                testValidationSchema,
            );

            expect(mockAddError).not.toHaveBeenCalled();
        });
    });

    describe('validation errors', () => {
        test('should add error for invalid string field', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return 'This title is too long';
                }

                return undefined;
            });

            validateFields(record, ['title'], testValidationSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'title',
                'Title too long',
            );
        });

        test('should add error for invalid email format', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'email') {
                    return 'invalid-email';
                }

                return undefined;
            });

            validateFields(record, ['email'], testValidationSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'email',
                'Invalid email format',
            );
        });

        test('should add error for invalid number field', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'age') {
                    return -5;
                }

                return undefined;
            });

            validateFields(record, ['age'], testValidationSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'age',
                'Age must be positive',
            );
        });

        test('should add error for invalid enum value', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'status') {
                    return 'INVALID_STATUS';
                }

                return undefined;
            });

            validateFields(record, ['status'], testValidationSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'status',
                'Invalid status',
            );
        });

        test('should add error for required field that is empty', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return '';
                }

                return undefined;
            });

            validateFields(record, ['title'], testValidationSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'title',
                'Title is required',
            );
        });
    });

    describe('multiple field validation', () => {
        test('should validate multiple fields and add errors for invalid ones', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return 'Valid';
                }
                if (key === 'email') {
                    return 'invalid-email';
                }
                if (key === 'age') {
                    return -5;
                }
                if (key === 'status') {
                    return 'ACTIVE';
                }

                return undefined;
            });

            validateFields(
                record,
                ['title', 'email', 'age', 'status'],
                testValidationSchema,
            );

            expect(mockAddError).toHaveBeenCalledWith(
                'email',
                'Invalid email format',
            );
            expect(mockAddError).toHaveBeenCalledWith(
                'age',
                'Age must be positive',
            );
            expect(mockAddError).toHaveBeenCalledTimes(2);
        });

        test('should handle empty recordKeys array', () => {
            const record = createMockRecord();
            const { addError: mockAddError } = record as MockedRecord;

            validateFields(record, [], testValidationSchema);

            expect(mockAddError).not.toHaveBeenCalled();
        });
    });

    describe('error tracking and deduplication', () => {
        test('should not add duplicate errors for the same field', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return 'This title is too long';
                }

                return undefined;
            });

            // Call validation twice
            validateFields(record, ['title'], testValidationSchema);
            validateFields(record, ['title'], testValidationSchema);

            // Should only add error once
            expect(mockAddError).toHaveBeenCalledTimes(1);
            expect(mockAddError).toHaveBeenCalledWith(
                'title',
                'Title too long',
            );
        });

        test('should re-add error if it was removed from flatfile', () => {
            const record = createMockRecord();
            const {
                get: mockGet,
                addError: mockAddError,
                getErrors: mockGetErrors,
            } = record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return 'This title is too long';
                }

                return undefined;
            });

            // First validation - error should be added
            validateFields(record, ['title'], testValidationSchema);
            expect(mockAddError).toHaveBeenCalledTimes(1);

            // Simulate error being removed in Flatfile by resetting getErrors to return empty array
            mockGetErrors.mockImplementation(() => {
                return []; // No errors for any field
            });

            // Second validation - error should be re-added
            validateFields(record, ['title'], testValidationSchema);
            expect(mockAddError).toHaveBeenCalledTimes(2);
        });

        test('should track errors across different fields', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return 'This title is too long';
                }
                if (key === 'email') {
                    return 'invalid-email';
                }

                return undefined;
            });

            // First validation
            validateFields(record, ['title'], testValidationSchema);
            // Second validation with different field
            validateFields(record, ['email'], testValidationSchema);
            // Third validation with both fields
            validateFields(record, ['title', 'email'], testValidationSchema);

            // Should add each error only once
            expect(mockAddError).toHaveBeenCalledWith(
                'title',
                'Title too long',
            );
            expect(mockAddError).toHaveBeenCalledWith(
                'email',
                'Invalid email format',
            );
            expect(mockAddError).toHaveBeenCalledTimes(2);
        });
    });

    describe('edge cases', () => {
        test('should handle undefined field values', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return undefined;
                }

                return undefined;
            });

            validateFields(record, ['title'], testValidationSchema);

            expect(mockAddError).toHaveBeenCalledWith('title', 'Required');
        });

        test('should handle null field values', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'title') {
                    return null;
                }

                return undefined;
            });

            validateFields(record, ['title'], testValidationSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'title',
                'Expected string, received null',
            );
        });

        test('should handle Zod error message parsing correctly', () => {
            // Create a schema that will produce a specific error format
            const customSchema = z.object({
                customField: z.string().min(5, 'Custom error message'),
            });

            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'customField') {
                    return 'abc'; // Too short
                }

                return undefined;
            });

            validateFields(record, ['customField'], customSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'customField',
                'Custom error message',
            );
        });

        test('should handle fields not in validation schema', () => {
            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'nonExistentField') {
                    return 'some value';
                }

                return undefined;
            });

            // This should not throw an error and should not add any errors
            // because the implementation gracefully handles non-existent fields
            expect(() => {
                validateFields(
                    record,
                    ['nonExistentField'],
                    testValidationSchema,
                );
            }).not.toThrow();

            // Should not add any errors for non-existent fields
            expect(mockAddError).not.toHaveBeenCalled();
        });

        test('should handle complex validation error messages', () => {
            const complexSchema = z.object({
                complexField: z
                    .string()
                    .refine((val) => val.includes('required-text'), {
                        message: 'Field must contain required-text',
                    }),
            });

            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'complexField') {
                    return 'invalid value';
                }

                return undefined;
            });

            validateFields(record, ['complexField'], complexSchema);

            expect(mockAddError).toHaveBeenCalledWith(
                'complexField',
                'Field must contain required-text',
            );
        });
    });

    describe('validation schema integration', () => {
        test('should work with different zod schema types', () => {
            const mixedSchema = z.object({
                stringField: z.string(),
                numberField: z.number(),
                booleanField: z.boolean(),
                arrayField: z.array(z.string()),
                optionalField: z.string().optional(),
            });

            const record = createMockRecord();
            const { get: mockGet, addError: mockAddError } =
                record as MockedRecord;

            mockGet.mockImplementation((key: string) => {
                if (key === 'stringField') {
                    return 123;
                } // Wrong type
                if (key === 'numberField') {
                    return 'not a number';
                } // Wrong type
                if (key === 'booleanField') {
                    return 'not a boolean';
                } // Wrong type
                if (key === 'arrayField') {
                    return 'not an array';
                } // Wrong type
                if (key === 'optionalField') {
                    return undefined;
                } // Valid (optional)

                return undefined;
            });

            validateFields(
                record,
                [
                    'stringField',
                    'numberField',
                    'booleanField',
                    'arrayField',
                    'optionalField',
                ],
                mixedSchema,
            );

            // Should add errors for type mismatches
            expect(mockAddError).toHaveBeenCalledTimes(4); // 4 invalid fields
            expect(mockAddError).not.toHaveBeenCalledWith(
                'optionalField',
                expect.anything(),
            );
        });
    });
});
