import { StatsBlock } from '@components/controls';
import { Button } from '@cosmos/components/button';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedControlsDetailsReadinessCardsData } from '@models/controls';
import { controlReviewCardModel } from '../control-review-approval/models/control-review-card.model';

export const ControlApprovalsReadinessCard = observer((): React.JSX.Element => {
    const { isLoading, approvalsReadinessCardData } =
        sharedControlsDetailsReadinessCardsData;
    const { dataLegendValues, statusLabel, iconName, iconColor } =
        approvalsReadinessCardData;
    const { hasWriteControlPermission } = sharedFeatureAccessModel;

    const emptyStateProps = {
        title: t`Require specific people to approve a control before it's ready.`,
        ...(hasWriteControlPermission && {
            leftAction: (
                <Button
                    label={t`Set up approvals`}
                    level="secondary"
                    onClick={action(() => {
                        controlReviewCardModel.handleEditClick();
                    })}
                />
            ),
        }),
    };

    return (
        <StatsBlock
            data-id="approvals-stats-block"
            data-testid="ApprovalsStatsBlock"
            title={t`Approvals`}
            isLoading={isLoading}
            iconName={iconName}
            iconColor={iconColor}
            statusLabel={statusLabel}
            legendValues={dataLegendValues}
            emptyStateProps={emptyStateProps}
        />
    );
});
