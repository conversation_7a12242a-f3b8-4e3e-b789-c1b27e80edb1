import { StatsBlock } from '@components/controls';
import { Button } from '@cosmos/components/button';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedControlsDetailsReadinessCardsData } from '@models/controls';
import { useNavigate } from '@remix-run/react';

export const ControlEvidenceReadinessCard = observer((): React.JSX.Element => {
    const { isLoading, evidenceReadinessCardData, controlId } =
        sharedControlsDetailsReadinessCardsData;
    const { dataLegendValues, statusLabel, iconName, iconColor } =
        evidenceReadinessCardData;
    const navigate = useNavigate();
    const { currentWorkspaceId } = sharedWorkspacesController;
    const { hasWriteControlPermission } = sharedFeatureAccessModel;

    const emptyStateProps = {
        title: t`Collect and store evidence for this control to demonstrate compliance.`,
        ...(hasWriteControlPermission && {
            leftAction: (
                <Button
                    label={t`Add evidence`}
                    level="secondary"
                    onClick={() => {
                        navigate(
                            `/workspaces/${currentWorkspaceId}/compliance/controls/${controlId}/evidence`,
                        );
                    }}
                />
            ),
        }),
    };

    return (
        <StatsBlock
            data-id="evidence-stats-block"
            data-testid="EvidenceStatsBlock"
            title={t`Evidence`}
            isLoading={isLoading}
            iconName={iconName}
            iconColor={iconColor}
            statusLabel={statusLabel}
            legendValues={dataLegendValues}
            emptyStateProps={emptyStateProps}
        />
    );
});
