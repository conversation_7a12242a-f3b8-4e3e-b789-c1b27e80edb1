import { noop } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { TextField, type TextFieldProps } from '@cosmos/components/text-field';
import { Divider } from '@cosmos-lab/components/divider';
import { ColoredSquare, type ColoredSquareProps } from './colored-square';

interface ThresholdSettingsItemProps {
    color: ColoredSquareProps['color'];
    formId: string;
    index: number;
    nameField: { label: string; value?: TextFieldProps['value'] };
    descriptionField: { label: string; value?: TextFieldProps['value'] };
}

export const ThresholdSettingsItem = ({
    color,
    formId,
    index,
    nameField,
    descriptionField,
}: ThresholdSettingsItemProps): React.JSX.Element => (
    <Stack
        direction="column"
        gap="2xl"
        data-testid="ThresholdSettingsItem"
        data-id="GzK-EDcT"
    >
        <Stack gap="3xl">
            <ColoredSquare color={color} />
            <Grid columns="1fr 1fr 1fr" rows="auto auto" flexGrow="1" gap="xl">
                <Box gridColumn="span 1">
                    <TextField
                        label={nameField.label}
                        formId={formId}
                        name={`${formId}-field${index}-name`}
                        value={nameField.value}
                        onChange={noop}
                    />
                </Box>
                <Stack gridColumn="span 3" align="end" gap="xl">
                    <TextField
                        label={descriptionField.label}
                        optionalText="optional"
                        formId={formId}
                        name={`${formId}-field${index}-name`}
                        value={descriptionField.value}
                        onChange={noop}
                    />
                    <Button
                        isIconOnly
                        label="Delete"
                        level="tertiary"
                        colorScheme="danger"
                        startIconName="Trash"
                        onClick={noop}
                    />
                </Stack>
            </Grid>
        </Stack>
        <Divider />
    </Stack>
);
