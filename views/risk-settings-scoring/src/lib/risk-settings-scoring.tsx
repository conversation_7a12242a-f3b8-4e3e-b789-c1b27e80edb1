import type React from 'react';
import { createNumericOptions } from '@controllers/risk';
import { sharedRiskSettingsController } from '@controllers/risk-settings';
import { Accordion } from '@cosmos/components/accordion';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Grid } from '@cosmos/components/grid';
import { Icon } from '@cosmos/components/icon';
import { SelectField } from '@cosmos/components/select-field';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import { Threshold } from '@cosmos-lab/components/threshold';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form } from '@ui/forms';

/**
 *All the todos in this page are going to be work on https://drata.atlassian.net/browse/ENG-73374.
 */
const handleSave = () => {
    //TODO: Implement save logic
};

export const RiskSettingsScoring = observer((): React.JSX.Element => {
    const { isLoading, riskSettings } = sharedRiskSettingsController;

    if (isLoading) {
        return <Skeleton barCount={10} />;
    }

    if (!riskSettings) {
        return (
            <EmptyState
                title="No Risk Settings Available"
                description="Risk settings could not be loaded. Please try refreshing the page."
            />
        );
    }

    const impactOptions = createNumericOptions(riskSettings.impact);
    const likelihoodOptions = createNumericOptions(riskSettings.likelihood);
    // TODO: Implement impact change riskScore model
    const riskScore = riskSettings.impact * riskSettings.likelihood;

    return (
        <Form
            hasExternalSubmitButton
            formId="risk-settings-scoring"
            data-id="vpMlcfzS"
            schema={{
                riskSettingsContent: {
                    type: 'custom',
                    label: 'Risk Settings Configuration',
                    render: () => (
                        <Stack
                            direction="column"
                            gap="2xl"
                            data-testid="RiskSettingsScoring"
                            data-id="pj5RFoRL"
                            width="100%"
                        >
                            <Text size="300" type="title">
                                <Trans>Impact & Likelihood</Trans>
                            </Text>

                            <Stack direction="row" gap="md" align="end">
                                <SelectField
                                    formId="risk-settings-form"
                                    label="Impact"
                                    name="impact-select"
                                    placeholder="Select impact level"
                                    options={impactOptions}
                                    value={impactOptions.find(
                                        (option) =>
                                            option.value ===
                                            String(riskSettings.impact),
                                    )}
                                    onChange={() => {
                                        // TODO: Implement impact change handler
                                    }}
                                />
                                <Icon name="Close" />
                                <SelectField
                                    formId="risk-settings-form"
                                    label="Likelihood"
                                    name="likelihood-select"
                                    placeholder="Select likelihood level"
                                    options={likelihoodOptions}
                                    value={likelihoodOptions.find(
                                        (option) =>
                                            option.value ===
                                            String(riskSettings.likelihood),
                                    )}
                                    onChange={() => {
                                        // TODO: Implement likelihood change handler
                                    }}
                                />
                                <Text size="500"> = </Text>
                                <RiskScore
                                    intensity="strong"
                                    severity="moderate"
                                    scoreNumber={riskScore}
                                    size="md"
                                />
                            </Stack>

                            <Stack
                                direction="column"
                                gap="md"
                                data-testid="RiskLevelDefinitionsAccordion"
                            >
                                <Accordion
                                    title="Impact Definitions"
                                    data-id="impact-definitions"
                                    body={
                                        <Stack direction="column" gap="md">
                                            <TextField
                                                label={''}
                                                formId={''}
                                                name={undefined}
                                                value={undefined}
                                                onChange={undefined}
                                            />
                                        </Stack>
                                    }
                                />
                                <Accordion
                                    title="Likelihood Definitions"
                                    data-id="likelihood-definitions"
                                    body={
                                        <Stack direction="column" gap="md">
                                            <TextField
                                                label={''}
                                                formId={''}
                                                name={undefined}
                                                value={undefined}
                                                onChange={undefined}
                                            />
                                        </Stack>
                                    }
                                />
                            </Stack>

                            <Grid
                                gap="2xl"
                                data-testid="RiskRegisterSettingsThresholdView"
                                data-id="jW0kzbrb"
                            >
                                <Text
                                    size="300"
                                    type="title"
                                    id="threshold-settings-label"
                                >
                                    <Trans>Thresholds</Trans>
                                </Text>
                                <Threshold
                                    min={1}
                                    max={5}
                                    displaySplitCount={5}
                                    initialValues={[1, 2, 3, 4, 5]}
                                    aria-labelledby="threshold-settings-label"
                                    id="risk-threshold-settings"
                                    name="thresholdBoundaries"
                                    onValueChange={() => {
                                        // TODO: Implement threshold boundary updates
                                    }}
                                />
                            </Grid>
                            <Box>
                                <Button
                                    type="submit"
                                    label="Save Settings"
                                    isLoading={isLoading}
                                    a11yLoadingLabel="Saving risk settings..."
                                />
                            </Box>
                        </Stack>
                    ),
                },
            }}
            onSubmit={handleSave}
        />
    );
});
