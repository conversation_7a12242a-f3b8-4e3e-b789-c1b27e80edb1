import { type AllowedBackgroundToken, Box } from '@cosmos/components/box';

export interface ColoredSquareProps {
    color?: Extract<AllowedBackgroundToken, `dataDiverge${string}`>;
}

export const ColoredSquare = ({
    color,
}: ColoredSquareProps): React.JSX.Element => {
    return (
        <Box
            backgroundColor={color}
            borderRadius="borderRadiusMd"
            width="5xl"
            height="5xl"
            data-testid="ColoredSquare"
            data-id="oCX4SGKJ"
        />
    );
};
