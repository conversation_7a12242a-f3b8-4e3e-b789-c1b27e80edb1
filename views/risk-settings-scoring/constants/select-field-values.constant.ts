import type {
    RiskScore,
    RiskScoreOption,
} from '../types/risk-register-settings.type';

const RISK_SCORE_LABELS: Record<RiskScore, string> = {
    1: '1',
    2: '2',
    3: '3',
    4: '4',
    5: '5',
    6: '6',
    7: '7',
    8: '8',
    9: '9',
    10: '10',
};

export const RISK_SCORE_OPTIONS: RiskScoreOption[] = Object.entries(
    RISK_SCORE_LABELS,
).map(([value, label], index) => {
    return {
        id: `${value}-${index}`,
        label,
        value,
    };
});
