import { head, isEmpty } from 'lodash-es';
import {
    REVIEW_APPROVAL_STATUS,
    type StatsBlockProps,
} from '@components/controls';
import {
    sharedControlApprovalsController,
    sharedControlsDetailsStatsController,
} from '@controllers/controls';
import {
    criticalBackgroundStrongInitial,
    neutralBackgroundStrong,
    successBackgroundModerate,
    successBackgroundStrong,
    warningBackgroundModerate,
} from '@cosmos/constants/tokens';
import {
    CheckResultStatus,
    CheckStatus as CheckStatusNumber,
    TestSource,
} from '@drata/enums';
import type {
    ApprovalsResponseDto,
    ControlReadyDetailsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

interface ReadinessCardData {
    dataLegendValues: StatsBlockProps['legendValues'];
    statusLabel: StatsBlockProps['statusLabel'];
    iconName?: StatsBlockProps['iconName'];
    iconColor?: StatsBlockProps['iconColor'];
}

interface EvidenceCounts {
    needsRenewal: number;
    needsSource: number;
    ready: number;
    notFactored: number;
    total: number;
}

interface MonitoringCounts {
    passing: number;
    failing: number;
    error: number;
    notFactored: number;
    total: number;
}

interface PoliciesCounts {
    ready: number;
    notReady: number;
    total: number;
}

class ControlsDetailsReadinessCardsData {
    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return sharedControlsDetailsStatsController.isLoading;
    }

    get controlId(): number | undefined {
        const { controlStats } = sharedControlsDetailsStatsController;

        return controlStats?.controlId;
    }

    get evidenceReadinessCardData(): ReadinessCardData {
        const { controlStats } = sharedControlsDetailsStatsController;

        const counts = this.getEvidenceCounts(controlStats);
        const dataLegendValues = this.buildEvidenceLegendValues(counts);
        const status = this.determineEvidenceStatus(counts);

        return {
            dataLegendValues,
            ...status,
        };
    }

    private getEvidenceCounts(
        controlStats: ControlReadyDetailsResponseDto | null,
    ): EvidenceCounts {
        if (!controlStats) {
            return {
                needsRenewal: 0,
                needsSource: 0,
                ready: 0,
                notFactored: 0,
                total: 0,
            };
        }

        const { evidence } = controlStats;

        return {
            needsRenewal:
                evidence.library.needsRenewal +
                evidence.external.notReady.length,
            needsSource: evidence.library.needsSource,
            ready:
                evidence.external.ready.length +
                evidence.library.ready.length +
                evidence.library.testResultsReady.length,
            notFactored: evidence.library.testResultsNotFactored.length,
            total:
                evidence.external.ready.length +
                evidence.external.notReady.length +
                evidence.library.ready.length +
                evidence.library.needsRenewal +
                evidence.library.needsSource +
                evidence.library.testResultsReady.length +
                evidence.library.testResultsNotFactored.length,
        };
    }

    private buildEvidenceLegendValues(
        counts: EvidenceCounts,
    ): StatsBlockProps['legendValues'] {
        if (counts.total === 0) {
            return [];
        }

        const legendItems = [];

        if (counts.needsRenewal > 0) {
            legendItems.push({
                label: t`Needs renewal`,
                value: counts.needsRenewal,
                color: criticalBackgroundStrongInitial,
            });
        }

        if (counts.needsSource > 0) {
            legendItems.push({
                label: t`Needs artifact`,
                value: counts.needsSource,
                color: criticalBackgroundStrongInitial,
            });
        }

        if (counts.ready > 0) {
            legendItems.push({
                label: t`Ready`,
                value: counts.ready,
                color:
                    counts.ready === counts.total
                        ? successBackgroundStrong
                        : successBackgroundModerate,
            });
        }

        if (counts.notFactored > 0) {
            legendItems.push({
                label: t`Not factored`,
                value: counts.notFactored,
                color: neutralBackgroundStrong,
                helpText: t`Evidence from erroring, disabled or unused tests do not factor into readiness.`,
            });
        }

        return legendItems;
    }

    private determineEvidenceStatus(counts: EvidenceCounts): {
        statusLabel: StatsBlockProps['statusLabel'];
        iconName?: StatsBlockProps['iconName'];
        iconColor?: StatsBlockProps['iconColor'];
    } {
        const notReadyCount = counts.needsRenewal + counts.needsSource;
        const isReady = notReadyCount === 0 && counts.ready === counts.total;
        const isNotFactored =
            counts.ready === 0 && counts.notFactored === counts.total;

        if (isNotFactored) {
            return { statusLabel: t`Not factored` };
        }

        if (isReady) {
            return {
                statusLabel: t`Ready`,
                iconName: 'CheckCircle',
                iconColor: 'success',
            };
        }

        return {
            statusLabel: t`Not ready`,
            iconName: 'NotReady',
            iconColor: 'critical',
        };
    }

    get monitoringReadinessCardData(): ReadinessCardData {
        const { controlStats } = sharedControlsDetailsStatsController;

        const counts = this.getMonitoringCounts(controlStats);
        const dataLegendValues = this.buildMonitoringLegendValues(counts);
        const status = this.determineMonitoringStatus(counts);

        return {
            dataLegendValues,
            ...status,
        };
    }

    private getMonitoringCounts(
        controlStats: ControlReadyDetailsResponseDto | null,
    ): MonitoringCounts {
        if (!controlStats) {
            return {
                passing: 0,
                failing: 0,
                error: 0,
                notFactored: 0,
                total: 0,
            };
        }

        const { tests } = controlStats;

        const testCheckResultStatuses = tests.checkResultStatuses ?? [];
        const testCheckSources = tests.checkSources ?? [];
        const testCheckStatuses = tests.checkStatuses ?? [];

        const inactiveTestsCount = testCheckStatuses.filter((status, index) => {
            return (
                status === (CheckStatusNumber.DISABLED as number) &&
                testCheckSources[index] !== (TestSource.ACORN as number)
            );
        }).length;

        const nonProductionTestsCount = testCheckSources.filter(
            (test) => test === (TestSource.ACORN as number),
        ).length;

        const readyTestCount = testCheckResultStatuses.filter(
            (status, index) => {
                return (
                    status === (CheckResultStatus.READY as number) &&
                    testCheckSources[index] !== (TestSource.ACORN as number) &&
                    testCheckStatuses[index] !==
                        (CheckStatusNumber.DISABLED as number)
                );
            },
        ).length;

        const preAuditToDoTestCount = testCheckResultStatuses.filter(
            (status) => {
                return status === (CheckResultStatus.PREAUDIT as number);
            },
        ).length;

        return {
            passing: tests.ready.length,
            failing: tests.notReady.length,
            error: testCheckResultStatuses.filter(
                (test) => test === (CheckResultStatus.ERROR as number),
            ).length,
            notFactored:
                inactiveTestsCount +
                nonProductionTestsCount +
                readyTestCount +
                preAuditToDoTestCount,
            total: testCheckStatuses.length,
        };
    }

    private buildMonitoringLegendValues(
        counts: MonitoringCounts,
    ): StatsBlockProps['legendValues'] {
        if (counts.total === 0) {
            return [];
        }

        const legendItems = [];

        if (counts.failing > 0) {
            legendItems.push({
                label: t`Failing`,
                value: counts.failing,
                color: criticalBackgroundStrongInitial,
            });
        }

        if (counts.passing > 0) {
            legendItems.push({
                label: t`Passing`,
                value: counts.passing,
                color:
                    counts.passing === counts.total
                        ? successBackgroundStrong
                        : successBackgroundModerate,
            });
        }

        if (counts.error > 0) {
            legendItems.push({
                label: t`Error`,
                value: counts.error,
                color: warningBackgroundModerate,
                helpText: t`Test errors are not factored into readiness.`,
            });
        }

        if (counts.notFactored > 0) {
            legendItems.push({
                label: t`Not factored`,
                value: counts.notFactored,
                color: neutralBackgroundStrong,
                helpText: t`Disabled, untested and non-production tests are not factored into readiness.`,
            });
        }

        return legendItems;
    }

    private determineMonitoringStatus(counts: MonitoringCounts): {
        statusLabel: StatsBlockProps['statusLabel'];
        iconName?: StatsBlockProps['iconName'];
        iconColor?: StatsBlockProps['iconColor'];
    } {
        const notReadyCount = counts.failing;
        const isReady = notReadyCount === 0 && counts.passing > 0;
        const isNotFactored =
            counts.failing === 0 &&
            counts.passing === 0 &&
            counts.notFactored + counts.error === counts.total &&
            counts.total > 0;

        if (isNotFactored) {
            return { statusLabel: t`Not factored` };
        }

        if (isReady) {
            return {
                statusLabel: t`Ready`,
                iconName: 'CheckCircle',
                iconColor: 'success',
            };
        }

        return {
            statusLabel: t`Not ready`,
            iconName: 'NotReady',
            iconColor: 'critical',
        };
    }

    get policiesReadinessCardData(): ReadinessCardData {
        const { controlStats } = sharedControlsDetailsStatsController;

        const counts = this.getPoliciesCounts(controlStats);
        const dataLegendValues = this.buildPoliciesLegendValues(counts);
        const status = this.determinePoliciesStatus(counts);

        return {
            dataLegendValues,
            ...status,
        };
    }

    private getPoliciesCounts(
        controlStats: ControlReadyDetailsResponseDto | null,
    ): PoliciesCounts {
        if (!controlStats) {
            return {
                ready: 0,
                notReady: 0,
                total: 0,
            };
        }

        const { policies } = controlStats;

        return {
            ready: policies.ready.length,
            notReady: policies.notReady.length,
            total: policies.ready.length + policies.notReady.length,
        };
    }

    private buildPoliciesLegendValues(
        counts: PoliciesCounts,
    ): StatsBlockProps['legendValues'] {
        if (counts.total === 0) {
            return [];
        }

        const legendItems = [];
        const isAllPoliciesReady = counts.ready === counts.total;

        if (counts.notReady > 0) {
            legendItems.push({
                label: t`Not published`,
                value: counts.notReady,
                color: criticalBackgroundStrongInitial,
            });
        }

        if (counts.ready > 0) {
            legendItems.push({
                label: t`Published`,
                value: counts.ready,
                color: isAllPoliciesReady
                    ? successBackgroundStrong
                    : successBackgroundModerate,
            });
        }

        return legendItems;
    }

    private determinePoliciesStatus(counts: PoliciesCounts): {
        statusLabel: StatsBlockProps['statusLabel'];
        iconName?: StatsBlockProps['iconName'];
        iconColor?: StatsBlockProps['iconColor'];
    } {
        const isReady = counts.ready > 0 && counts.ready === counts.total;

        if (isReady) {
            return {
                statusLabel: t`Ready`,
                iconName: 'CheckCircle',
                iconColor: 'success',
            };
        }

        return {
            statusLabel: t`Not ready`,
            iconName: 'NotReady',
            iconColor: 'critical',
        };
    }

    get approvalsReadinessCardData(): ReadinessCardData {
        const { controlApprovals } = sharedControlApprovalsController;

        const dataLegendValues =
            this.buildApprovalsLegendValues(controlApprovals);
        const status = this.determineApprovalsStatus(controlApprovals);

        return {
            dataLegendValues,
            ...status,
        };
    }

    private buildApprovalsLegendValues(
        controlApprovals: ApprovalsResponseDto[],
    ): StatsBlockProps['legendValues'] {
        const reviewer = controlApprovals.map(
            (approval) => approval.lastReview?.reviewer,
        );
        const showEmptyState =
            isEmpty(reviewer) || reviewer.every((item) => item === undefined);

        if (showEmptyState) {
            return [];
        }

        const legendItems = [];

        const controlIsApproved =
            head(controlApprovals)?.approvalStatus ===
            REVIEW_APPROVAL_STATUS.COMPLETED;
        const currentApproval = controlApprovals.find(
            (approval) => approval.current,
        );

        const controlWaitingApproval =
            currentApproval?.approvalStatus !==
                REVIEW_APPROVAL_STATUS.INITIALIZE &&
            currentApproval?.approvalStatus !==
                REVIEW_APPROVAL_STATUS.COMPLETED;

        if (controlIsApproved) {
            legendItems.push({
                label: t`Approved`,
                color: successBackgroundStrong,
                value: 1,
            });
        }

        if (controlWaitingApproval) {
            legendItems.push({
                label: t`Needs approval`,
                color: criticalBackgroundStrongInitial,
                value: 1,
            });
        }

        return legendItems;
    }

    private determineApprovalsStatus(
        controlApprovals: ApprovalsResponseDto[],
    ): {
        statusLabel: StatsBlockProps['statusLabel'];
        iconName?: StatsBlockProps['iconName'];
        iconColor?: StatsBlockProps['iconColor'];
    } {
        const currentApproval = controlApprovals.find(
            (approval) => approval.current,
        );
        const isApproved =
            currentApproval?.approvalStatus ===
            REVIEW_APPROVAL_STATUS.COMPLETED;

        if (isApproved) {
            return {
                statusLabel: t`Ready`,
                iconName: 'CheckCircle',
                iconColor: 'success',
            };
        }

        return {
            statusLabel: t`Not ready`,
            iconName: 'NotReady',
            iconColor: 'critical',
        };
    }
}

export const sharedControlsDetailsReadinessCardsData =
    new ControlsDetailsReadinessCardsData();
