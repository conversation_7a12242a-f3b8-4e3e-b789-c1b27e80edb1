import { isEmpty, isNil } from 'lodash-es';
import {
    SEND_QUESTIONNAIRE_MODAL_ID,
    UPLOAD_RESPONSE_FILE_MODAL_ID,
    type VendorListBoxItemData,
} from '@components/vendors-prospective-add';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    vendorsControllerCreateVendorMutation,
    vendorsDocumentsControllerUploadVendorDocumentMutation,
    vendorsQuestionnairesControllerSaveManualQuestionnaireFilesMutation,
    vendorsQuestionnairesControllerSendQuestionnaireEmailMutation,
    vendorsSecurityReviewsControllerCreateVendorSecurityReviewMutation,
} from '@globals/api-sdk/queries';
import type {
    SendQuestionnaireRequestDto,
    VendorRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    toJS,
    when,
} from '@globals/mobx';
import type { FormValues } from '@ui/forms';
import {
    CREATING_VENDOR_ERROR,
    SEND_QUESTIONNAIRE_EMAIL_ERROR,
    SEND_QUESTIONNAIRE_EMAIL_SUCCESS,
    UPLOAD_FILE_ERROR,
    UPLOAD_FILE_SUCCESS,
} from './constants/vendors-create-snackbars.constants';
import { sharedVendorsProfileReportsAndDocumentsController } from './vendors-profile-reports-and-documents-controller';

class VendorsCreateVendorController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorId: number | null = null;
    securityReviewId: number | undefined = undefined;
    vendorName = '';
    isCustomVendor = false;
    vendorDetails: FormValues | null = null;
    vendorImpactAssessmentDetails: FormValues | null = null;
    vendorProspectiveQuestionnaires: FormValues[] = [];
    vendorProspectiveManualQuestionnaires: File[] = [];

    createVendorMutation = new ObservedMutation(
        vendorsControllerCreateVendorMutation,
    );
    createVendorSecurityReviewMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerCreateVendorSecurityReviewMutation,
    );
    uploadFileQuery = new ObservedMutation(
        vendorsDocumentsControllerUploadVendorDocumentMutation,
    );
    sendQuestionnaireEmailMutation = new ObservedMutation(
        vendorsQuestionnairesControllerSendQuestionnaireEmailMutation,
        {
            onSuccess: () => {
                sharedVendorsProfileReportsAndDocumentsController.paginatedQuestionnaires.invalidate();
            },
        },
    );

    sendManualQuestionnaireMutation = new ObservedMutation(
        vendorsQuestionnairesControllerSaveManualQuestionnaireFilesMutation,
        {
            onSuccess: () => {
                sharedVendorsProfileReportsAndDocumentsController.paginatedQuestionnaires.invalidate();
            },
        },
    );

    get isLastStepLoading(): boolean {
        return (
            this.createVendorMutation.isPending ||
            this.createVendorSecurityReviewMutation.isPending ||
            this.uploadFileQuery.isPending ||
            this.sendQuestionnaireEmailMutation.isPending ||
            this.sendManualQuestionnaireMutation.isPending
        );
    }

    saveVendorDetails = (formValues: FormValues) => {
        const businessUnit = formValues.category as ListBoxItemData | undefined;
        const internalSecurityOwner = formValues.internalSecurityOwner as
            | ListBoxItemData
            | undefined;

        const selectedVendor = formValues.name as VendorListBoxItemData;

        this.vendorName = selectedVendor.label;
        this.isCustomVendor = selectedVendor.isCustom ?? false;

        const newValues: FormValues = {
            name: selectedVendor.label,
            url: isEmpty(formValues.url) ? undefined : formValues.url,
            category: businessUnit?.value ?? undefined,
            servicesProvided: isEmpty(formValues.servicesProvided)
                ? undefined
                : formValues.servicesProvided,
            contactAtVendor: isEmpty(formValues.contactAtVendor)
                ? undefined
                : formValues.contactAtVendor,
            contactsEmail: isEmpty(formValues.contactsEmail)
                ? undefined
                : formValues.contactsEmail,
            userId: Number((formValues.requester as ListBoxItemData).id),
            requester: formValues.requester ?? undefined,
            contact: internalSecurityOwner
                ? { id: Number(internalSecurityOwner.value) }
                : undefined,
            internalSecurityOwner: internalSecurityOwner ?? undefined,
            requestDate: formValues.requestDate ?? undefined,
            renewalDate: formValues.renewalDate ?? undefined,
        };

        this.vendorDetails = newValues;
    };

    saveImpactAssessmentDetails = (formValues: FormValues) => {
        const impactLevel = formValues.impactLevel as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            dataAccessedOrProcessedList: isEmpty(
                formValues.dataAccessedOrProcessedList,
            )
                ? undefined
                : formValues.dataAccessedOrProcessedList,
            operationalImpact: isEmpty(formValues.operationalImpact)
                ? undefined
                : formValues.operationalImpact,
            environmentAccess: isEmpty(formValues.environmentAccess)
                ? undefined
                : formValues.environmentAccess,
            impactLevel: impactLevel?.value ?? undefined,
        };

        this.vendorImpactAssessmentDetails = newValues;
    };

    saveFilesDetails = (formValues: FormValues) => {
        const vendorFiles = formValues.vendorFiles as FormValues | undefined;
        const newValues: FormValues = {
            documents: vendorFiles?.documents ?? undefined,
        };

        this.createProspectiveVendorDetails(newValues);
    };

    createProspectiveVendorDetails = (newValues: FormValues) => {
        const mutatedVendorDetails = {
            ...this.vendorDetails,
            ...this.vendorImpactAssessmentDetails,
            status: 'PROSPECTIVE',
            isSubProcessor: false,
            isSubProcessorActive: false,
            hasPii: false,
            passwordRequiresMinLength: false,
            passwordMinLength: null,
            passwordRequiresNumber: false,
            passwordRequiresSymbol: false,
            passwordMfaEnabled: false,
        } as VendorRequestDto;

        this.createVendorMutation.mutate({
            body: mutatedVendorDetails,
        });
        when(
            () => !this.createVendorMutation.isPending,
            () => {
                const { response, hasError } = this.createVendorMutation;

                if (hasError) {
                    snackbarController.addSnackbar(CREATING_VENDOR_ERROR);

                    return;
                }

                if (response?.id) {
                    this.vendorId = response.id;

                    this.createVendorSecurityReviewMutation.mutate({
                        body: {
                            requesterUserId: Number(
                                mutatedVendorDetails.userId,
                            ),
                            requestedAt: this.vendorDetails?.requestDate as
                                | string
                                | undefined,
                            reviewDeadlineAt: response.renewalDate as string,
                            securityReviewStatus: 'IN_PROGRESS',
                            securityReviewType: 'SECURITY',
                        },
                        path: { vendorId: response.id },
                    });
                    when(
                        () =>
                            !this.createVendorSecurityReviewMutation.isPending,
                        () => {
                            const {
                                response: responseSecurity,
                                hasError: hasErrorSecurity,
                            } = this.createVendorSecurityReviewMutation;

                            if (hasErrorSecurity) {
                                snackbarController.addSnackbar(
                                    CREATING_VENDOR_ERROR,
                                );

                                return;
                            }

                            if (responseSecurity?.id) {
                                this.securityReviewId = responseSecurity.id;
                            } else {
                                console.error(
                                    'Failed to get security review ID from response',
                                );
                            }
                        },
                    );

                    this.uploadComplianceReports(newValues, response.id);
                }
            },
        );
    };

    uploadComplianceReports = (newValues: FormValues, vendorId: number) => {
        const documents = newValues.documents as File[] | undefined;

        if (isNil(documents) || isEmpty(documents)) {
            this.sendQuestionnaireEmails(vendorId);
        } else {
            Promise.allSettled(
                documents.map(async (file: File) => {
                    return this.uploadFileQuery.mutateAsync({
                        body: {
                            file,
                            type: 'COMPLIANCE_REPORT',
                        },
                        path: { id: vendorId },
                    });
                }),
            )
                .then((result) => {
                    const failedUploads = result.filter(
                        (r) => r.status === 'rejected',
                    );

                    if (!isEmpty(failedUploads)) {
                        const failedCount = failedUploads.length;

                        snackbarController.addSnackbar({
                            ...UPLOAD_FILE_ERROR,
                            props: {
                                ...UPLOAD_FILE_ERROR.props,
                                description: t`Failed to upload ${failedCount} file(s). Please try again later.`,
                            },
                        });
                    }
                })
                .catch((error) => {
                    console.error('File upload operation failed:', error);
                })
                .finally(() => {
                    this.sendQuestionnaireEmails(vendorId);
                });
        }
    };

    sendQuestionnaireEmails = (vendorId: number) => {
        if (isNil(this.securityReviewId)) {
            console.error('SecurityReviewId is not defined');

            return;
        }
        const questionnaires: SendQuestionnaireRequestDto[] = toJS(
            this.vendorProspectiveQuestionnaires,
        ).map((questionnaire) => {
            return {
                email: String(questionnaire.sendTo),
                emailContent: String(questionnaire.message),
                questionnaireId: Number(
                    (questionnaire.questionnaires as ListBoxItemData).id,
                ),
                securityReviewId: Number(this.securityReviewId),
            };
        });

        if (isEmpty(questionnaires) || isNil(questionnaires)) {
            this.sendManualQuestionnaires(vendorId);
        } else {
            Promise.allSettled(
                questionnaires.map(async (questionnaire) => {
                    if (isNil(this.securityReviewId)) {
                        console.error('SecurityReviewId is not defined');

                        return;
                    }

                    return this.sendQuestionnaireEmailMutation.mutateAsync({
                        body: {
                            email: questionnaire.email
                                .replaceAll(', ', ';')
                                .replaceAll(',', ';'),
                            emailContent: questionnaire.emailContent,
                            questionnaireId: questionnaire.questionnaireId,
                            securityReviewId: Number(this.securityReviewId),
                        },
                        path: { id: vendorId },
                    });
                }),
            )
                .then((result) => {
                    const failedEmails = result.filter(
                        (r) => r.status === 'rejected',
                    );

                    if (isEmpty(failedEmails)) {
                        snackbarController.addSnackbar(
                            SEND_QUESTIONNAIRE_EMAIL_SUCCESS,
                        );
                    } else {
                        const failedCount = failedEmails.length;

                        snackbarController.addSnackbar({
                            ...SEND_QUESTIONNAIRE_EMAIL_ERROR,
                            props: {
                                ...SEND_QUESTIONNAIRE_EMAIL_ERROR.props,
                                description: t`Failed to send email to ${failedCount} recipient(s). Please try again later.`,
                            },
                        });
                    }
                })
                .catch(() => {
                    snackbarController.addSnackbar(
                        SEND_QUESTIONNAIRE_EMAIL_ERROR,
                    );
                })
                .finally(() => {
                    this.sendManualQuestionnaires(vendorId);
                });
        }
    };

    sendManualQuestionnaires = (vendorId: number) => {
        const manualFiles = this.vendorProspectiveManualQuestionnaires as
            | File[]
            | undefined;

        if (!isNil(manualFiles) && !isEmpty(manualFiles)) {
            this.sendManualQuestionnaireMutation
                .mutateAsync({
                    body: {
                        'uploadedFiles[]': manualFiles,
                    },
                    path: { id: vendorId },
                })

                .then(() => {
                    snackbarController.addSnackbar(UPLOAD_FILE_SUCCESS);
                })

                .catch(() => {
                    snackbarController.addSnackbar({
                        ...UPLOAD_FILE_ERROR,
                        id: 'upload-manual-questionnaire-error',
                        props: {
                            ...UPLOAD_FILE_ERROR.props,
                            description: t`Failed to upload manual questionnaire file(s). Please try again later.`,
                        },
                    });
                });
        }
    };

    saveVendorProspectiveQuestionnaires = (formValues: FormValues) => {
        this.vendorProspectiveQuestionnaires.push(formValues);
        modalController.closeModal(SEND_QUESTIONNAIRE_MODAL_ID);
    };

    saveVendorProspectiveManualQuestionnaires = (formValues: FormValues) => {
        const files = formValues.files as File[] | undefined;

        if (files) {
            const newFiles = files.filter(
                (newFile) =>
                    !this.vendorProspectiveManualQuestionnaires.some(
                        (existingFile) =>
                            existingFile.name === newFile.name &&
                            existingFile.size === newFile.size &&
                            existingFile.lastModified === newFile.lastModified,
                    ),
            );

            this.vendorProspectiveManualQuestionnaires.push(...newFiles);
        }
        modalController.closeModal(UPLOAD_RESPONSE_FILE_MODAL_ID);
    };

    handleRemoveProspectiveQuestionnaires = (index: number) => {
        this.vendorProspectiveQuestionnaires.splice(index, 1);
    };

    handleRemoveProspectiveManualQuestionnaires = (index: number) => {
        this.vendorProspectiveManualQuestionnaires.splice(index, 1);
    };
}

export const sharedVendorsCreateVendorController =
    new VendorsCreateVendorController();
