import { vendorsSecurityReviewsControllerGetVendorsSecurityReviewOptions } from '@globals/api-sdk/queries';
import type { VendorSecurityReviewResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { buildTitle } from './helpers/vendors-security-review.helper';

class VendorsSecurityReviewDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    securityReviewDetailsQuery = new ObservedQuery(
        vendorsSecurityReviewsControllerGetVendorsSecurityReviewOptions,
    );

    get isLoading(): boolean {
        return this.securityReviewDetailsQuery.isLoading;
    }

    get securityReviewDetails(): VendorSecurityReviewResponseDto | null {
        const { data } = this.securityReviewDetailsQuery;

        if (!data) {
            return null;
        }

        return {
            ...data,
            title:
                data.title ??
                buildTitle({
                    type: data.type,
                    date: data.requestedAt,
                }),
        };
    }

    loadSecurityReviewDetails = (
        options: Parameters<typeof this.securityReviewDetailsQuery.load>[0],
    ) => {
        this.securityReviewDetailsQuery.load(options);
    };
}

export const sharedVendorsSecurityReviewDetailsController =
    new VendorsSecurityReviewDetailsController();
