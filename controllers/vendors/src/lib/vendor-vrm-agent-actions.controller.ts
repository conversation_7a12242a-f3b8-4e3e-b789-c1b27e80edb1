import { makeAutoObservable } from '@globals/mobx';
import { VRM_AGENT_ACTION_MAPPING } from './constants/vrm-agent-actions-mapping.constants';
import type { VrmAgentActionId } from './types/vendor-vrm-agent.types';

export class VendorVrmAgentActionsController {
    constructor() {
        makeAutoObservable(this);
    }

    executeAction = (actionId: string, vendorId: string): void => {
        if (!(actionId in VRM_AGENT_ACTION_MAPPING)) {
            console.warn(`Unknown action ID: ${actionId}`);

            return;
        }

        const handlerName =
            VRM_AGENT_ACTION_MAPPING[actionId as VrmAgentActionId];
        const handler = this.getActionHandler(handlerName);

        if (handler) {
            handler(vendorId);
        } else {
            console.warn(`No handler found for action: ${actionId}`);
        }
    };

    getActionHandler = (
        handlerName: string,
    ): ((vendorId: string) => void) | null => {
        switch (handlerName) {
            case 'handleAssessmentStart': {
                return this.handleAssessmentStart;
            }
            case 'handleStopAnalysis': {
                return this.handleStopAnalysis;
            }
            case 'handleSendQuestionnaire': {
                return this.handleSendQuestionnaire;
            }
            case 'handleAutoSendQuestionnaire': {
                return this.handleAutoSendQuestionnaire;
            }
            case 'handleFinalizeAssessment': {
                return this.handleFinalizeAssessment;
            }
            default: {
                return null;
            }
        }
    };
    handleAssessmentStart = (vendorId: string): void => {
        console.info('Starting VRM assessment for vendor:', vendorId);
    };

    handleStopAnalysis = (vendorId: string): void => {
        console.info('Stopping VRM analysis for vendor:', vendorId);
    };
    handleSendQuestionnaire = (vendorId: string): void => {
        console.info('Sending questionnaire for vendor:', vendorId);
    };
    handleAutoSendQuestionnaire = (vendorId: string): void => {
        console.info('Enabling auto-send questionnaires for vendor:', vendorId);
    };

    handleFinalizeAssessment = (vendorId: string): void => {
        console.info('Finalizing assessment for vendor:', vendorId);
    };

    hasHandler = (actionId: string): boolean => {
        return actionId in VRM_AGENT_ACTION_MAPPING;
    };
}

export const sharedVendorVrmAgentActionsController =
    new VendorVrmAgentActionsController();
