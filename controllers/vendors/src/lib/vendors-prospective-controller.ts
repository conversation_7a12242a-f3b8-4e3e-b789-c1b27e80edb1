import { isEmpty, isString } from 'lodash-es';
import type { ProspectiveVendor } from '@components/vendors-prospective';
import { calculateReviewDeadlineDate } from '@components/vendors-security-reviews';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import {
    vendorsControllerListVendorsOptions,
    vendorsControllerUpdateVendorMutation,
    vendorsSecurityReviewsControllerGetSecurityReviewDocumentsOptions,
} from '@globals/api-sdk/queries';
import type {
    VendorReduceDirectoryResponseDto,
    VendorRequestDto,
    VendorResponseDto,
} from '@globals/api-sdk/types';
import { zVendorRequestDto } from '@globals/api-sdk/zod';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { convertToISO8601String } from '@helpers/date-time';
import { IMPACT_LEVEL_VALUES } from '../../../../models/vendors-profile/src/lib/constants/vendors-current.constants';
import { buildProspectiveVendorQuery } from '../../../../views/vendors-prospective/src/lib/helpers/build-prospective-vendor-query.helper';
import {
    SECURITY_REVIEW_DOCUMENTS_INITIAL_PAGE,
    SECURITY_REVIEW_DOCUMENTS_PAGE_SIZE,
    VENDORS_PROSPECTIVE_DEFAULT_PARAMS,
} from './constants/vendors-prospective-controller.constants';
import { getProspectiveSortMapping } from './constants/vendors-prospective-controller-sort.constants';
import { formatVendorsProspectiveFilters } from './helpers/format-vendors-filters.helper';
import { findRelevantQuestionnaire } from './helpers/vendors-security-review-documents-adapter.helper';
import type { QuestionnaireData, VendorsQuery } from './types/vendors.type';
import { sharedVendorsCurrentController } from './vendors-current-controller';
import { sharedVendorsDetailsController } from './vendors-details-controller';
import { sharedVendorsReportDownloadController } from './vendors-report-download-controller';
import { sharedVendorsSchedulesQuestionnairesController } from './vendors-schedules-questionnaires-controller';

class VendorsProspectiveController {
    tableParams?: FetchDataResponseParams;
    hasFilters = false;

    /**
     * Map to store questionnaire data for each vendor security review.
     */
    vendorQuestionnaireData = new Map<number, QuestionnaireData>();

    constructor() {
        makeAutoObservable(this);
    }

    allVendorsProspectiveQuery = new ObservedQuery(
        vendorsControllerListVendorsOptions,
    );

    updateVendor = new ObservedMutation(vendorsControllerUpdateVendorMutation);

    get allVendorsProspective(): ProspectiveVendor[] {
        return (
            this.allVendorsProspectiveQuery.data?.data.map((vendor) => {
                const securityReviewId = vendor.securityReview?.id;
                const baseStatus =
                    vendor.securityReview?.status ?? 'NOT_YET_STARTED';

                // Load documents for IN_PROGRESS vendors
                if (
                    baseStatus === 'IN_PROGRESS' &&
                    securityReviewId &&
                    !this.vendorQuestionnaireData.has(securityReviewId)
                ) {
                    this.loadSecurityReviewDocuments(securityReviewId);
                }

                const reviewStatusDescription = this.getReviewStatusDescription(
                    vendor,
                    baseStatus,
                    securityReviewId,
                );

                return {
                    id: vendor.id,
                    name: vendor.name,
                    reviewStatus: baseStatus,
                    reviewStatusDescription,
                    deadline: vendor.renewalDate || '',
                    requestedAt: vendor.securityReview?.requestedAt || '',
                    impactLevel: vendor.impactLevel,
                    category: vendor.category,
                    // TODO: https://drata.atlassian.net/browse/ENG-65689
                    isOverdue: false,
                    isOverdueSoon: false,
                };
            }) ?? []
        );
    }

    get isLoading() {
        return this.allVendorsProspectiveQuery.isLoading;
    }

    get total() {
        return this.allVendorsProspectiveQuery.data?.total ?? 0;
    }

    get isUpdating() {
        return this.updateVendor.isPending;
    }

    get updateError() {
        return this.updateVendor.error;
    }

    loadVendorsProspective = (params: FetchDataResponseParams) => {
        // Update table params and hasFilters state
        this.tableParams = params;
        this.hasFilters =
            !isEmpty(params.globalFilter.search?.trim()) ||
            !isEmpty(params.globalFilter.filters);

        const { pagination, globalFilter, sorting } = params;
        const { filters, search } = globalFilter;

        const { impactLevel, reviewStatus } =
            formatVendorsProspectiveFilters(filters);

        const query: VendorsQuery = {
            page: pagination.page,
            limit: pagination.pageSize,
            q: search,
            status: 'PROSPECTIVE',
            impactLevel,
            reviewStatus,
        };

        if (!isEmpty(sorting) && sorting[0]?.id) {
            const { id: sortId, desc } = sorting[0];

            if (isString(sortId)) {
                const mappedSort = getProspectiveSortMapping(sortId);

                if (mappedSort) {
                    query.sort =
                        mappedSort as NonNullable<VendorsQuery>['sort'];
                    query.sortDir = desc ? 'DESC' : 'ASC';
                }
            }
        }

        this.allVendorsProspectiveQuery.load({
            query,
        });
    };

    loadVendorsProspectiveInitial = () => {
        this.loadVendorsProspective(VENDORS_PROSPECTIVE_DEFAULT_PARAMS);
    };

    getReviewStatusDescription = (
        vendor: VendorReduceDirectoryResponseDto,
        baseStatus: string,
        securityReviewId?: number,
    ): string => {
        let reviewStatusDescription = vendor.securityReview?.decision || '';

        if (baseStatus === 'IN_PROGRESS' && securityReviewId) {
            const questionnaireData =
                this.vendorQuestionnaireData.get(securityReviewId);

            if (questionnaireData) {
                const { isCompleted, isManualUpload } = questionnaireData;

                if (!isCompleted && !isManualUpload) {
                    reviewStatusDescription = 'QUESTIONNAIRE_SENT';
                } else if (!isCompleted) {
                    reviewStatusDescription = 'QUESTIONNAIRE_READY_FOR_REVIEW';
                }
            }
        }

        return reviewStatusDescription;
    };

    /**
     * Loads security review documents for a vendor and extracts questionnaire data
     * to determine the correct status description.
     */
    loadSecurityReviewDocuments = (securityReviewId: number) => {
        // Create a new ObservedQuery for each call to avoid conflicts
        const documentsQuery = new ObservedQuery(
            vendorsSecurityReviewsControllerGetSecurityReviewDocumentsOptions,
        );

        documentsQuery.load({
            path: { id: securityReviewId },
            query: {
                page: SECURITY_REVIEW_DOCUMENTS_INITIAL_PAGE,
                limit: SECURITY_REVIEW_DOCUMENTS_PAGE_SIZE,
            },
        });

        when(
            () => !documentsQuery.isLoading,
            () => {
                const documents = documentsQuery.data?.data ?? [];
                const targetQuestionnaire =
                    findRelevantQuestionnaire(documents);

                if (targetQuestionnaire?.questionnaire) {
                    const { questionnaire } = targetQuestionnaire;
                    const questionnaireData = {
                        isCompleted: questionnaire.isCompleted,
                        isManualUpload: questionnaire.isManualUpload,
                        dateSent: questionnaire.dateSent,
                    };

                    this.vendorQuestionnaireData.set(
                        securityReviewId,
                        questionnaireData,
                    );
                }
            },
        );
    };

    updateVendorDetails = (
        vendorId: number,
        updateData: VendorRequestDto,
    ): void => {
        const dataWithDefaults = {
            ...updateData,
            id: vendorId,
            location: updateData.location ?? '',
            operationalImpact: updateData.operationalImpact ?? 'NONE',
            environmentAccess: updateData.environmentAccess ?? 'NO',
            impactLevel: updateData.impactLevel ?? IMPACT_LEVEL_VALUES[5],
            integrations: updateData.integrations ?? [],
            notes: updateData.notes ?? undefined,
        };

        const validatedData = zVendorRequestDto.parse(dataWithDefaults);

        this.updateVendor.mutate({
            path: { id: vendorId },
            body: validatedData,
        });

        when(
            () => !this.updateVendor.isPending,
            () => {
                if (!this.updateVendor.hasError) {
                    this.allVendorsProspectiveQuery.invalidate();
                }
            },
        );
    };

    /**
     * Downloads a filtered report of prospective vendors.
     * Only works when filters are applied to the table.
     */
    downloadFilteredReport = () => {
        if (!this.hasFilters || !this.tableParams) {
            return;
        }

        const { globalFilter, sorting } = this.tableParams;
        const { filters, search } = globalFilter;

        const formattedFilters = formatVendorsProspectiveFilters(filters);

        const query = buildProspectiveVendorQuery(
            search || '',
            formattedFilters as unknown as Record<string, unknown>,
            sorting,
        );

        const hasActualFilters = Boolean(
            search?.trim() ||
                formattedFilters.impactLevel ||
                formattedFilters.reviewStatus,
        );

        if (!hasActualFilters) {
            return;
        }

        sharedVendorsReportDownloadController.downloadProspectiveReport(query);
    };

    /**
     * Activates a prospective vendor by updating its status to ACTIVE and setting up recurring reviews if needed.
     * This method handles the complete flow including questionnaire scheduling and query invalidation.
     */
    activateProspectiveVendor = (
        vendor: VendorResponseDto,
        activationData: {
            risk: VendorRequestDto['risk'];
            renewalScheduleType: VendorResponseDto['renewalScheduleType'];
            reminderToggle: boolean;
            scheduleToggle: boolean;
            questionnaireIds: number[];
            contactsEmail: string | null;
        },
        onSuccess?: () => void,
    ): void => {
        // Calculate renewal date if recurring reviews are enabled
        let renewalDate = null;
        let renewalDateStatus = 'NO_RENEWAL';

        if (
            activationData.reminderToggle &&
            activationData.renewalScheduleType !== 'NONE'
        ) {
            const calculatedDate = calculateReviewDeadlineDate(
                activationData.renewalScheduleType,
            );

            renewalDate = convertToISO8601String(calculatedDate);
            renewalDateStatus = ''; // Empty string lets backend calculate the correct status
        }

        // Prepare vendor update data
        const updateData: VendorRequestDto = {
            ...vendor,
            status: 'ACTIVE',
            risk: activationData.risk,
            renewalScheduleType: activationData.renewalScheduleType,
            renewalDate,
            renewalDateStatus,
            integrations: vendor.integrations?.map(
                (integration) => integration.id,
            ),
            passwordPolicy:
                (vendor.passwordPolicy as string | null) ?? undefined,
            type: (vendor.type as string | null) ?? undefined,
        } as unknown as VendorRequestDto;

        // Update vendor details
        this.updateVendorDetails(vendor.id, updateData);

        // Wait for vendor update to complete, then handle questionnaires
        when(
            () => !this.isUpdating,
            () => {
                if (this.updateVendor.hasError) {
                    logger.error({
                        message: 'Failed to mark vendor as active',
                        additionalInfo: {
                            vendorId: vendor.id,
                            vendorName: vendor.name,
                            activationData: {
                                risk: activationData.risk,
                                renewalScheduleType:
                                    activationData.renewalScheduleType,
                                reminderToggle: activationData.reminderToggle,
                                scheduleToggle: activationData.scheduleToggle,
                                questionnaireIds:
                                    activationData.questionnaireIds,
                                contactsEmail: activationData.contactsEmail,
                            },
                            error: this.updateVendor.error,
                        },
                    });

                    return;
                }

                // Create questionnaire schedules if needed
                if (
                    activationData.scheduleToggle &&
                    !isEmpty(activationData.questionnaireIds)
                ) {
                    sharedVendorsSchedulesQuestionnairesController.createSchedulesVendorQuestionnaires(
                        vendor.id,
                        activationData.questionnaireIds,
                        activationData.contactsEmail ?? '',
                    );

                    // Wait for questionnaire scheduling to complete
                    when(
                        () =>
                            !sharedVendorsSchedulesQuestionnairesController.isUpdating,
                        () => {
                            if (
                                sharedVendorsSchedulesQuestionnairesController
                                    .createSchedulesVendorQuestionnairesMutation
                                    .hasError
                            ) {
                                logger.error({
                                    message:
                                        'Failed to schedule questionnaires during vendor activation',
                                    additionalInfo: {
                                        vendorId: vendor.id,
                                        vendorName: vendor.name,
                                        questionnaireIds:
                                            activationData.questionnaireIds,
                                        contactsEmail:
                                            activationData.contactsEmail,
                                        error: sharedVendorsSchedulesQuestionnairesController
                                            .createSchedulesVendorQuestionnairesMutation
                                            .error,
                                    },
                                });
                            }

                            this.handleActivationSuccess(onSuccess);
                        },
                    );
                } else {
                    this.handleActivationSuccess(onSuccess);
                }
            },
        );
    };

    handleActivationSuccess = (onSuccess?: () => void): void => {
        sharedVendorsDetailsController.vendorDetailsQuery.invalidate();
        this.allVendorsProspectiveQuery.invalidate();
        sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();

        onSuccess?.();
    };
}

export const sharedVendorsProspectiveController =
    new VendorsProspectiveController();
