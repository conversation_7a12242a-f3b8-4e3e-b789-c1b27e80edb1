import { snackbarController } from '@controllers/snackbar';
import {
    vendorsSecurityReviewsControllerCreateSecurityReviewObservationMutation,
    vendorsSecurityReviewsControllerDeleteSecurityReviewObservationMutation,
    vendorsSecurityReviewsControllerUpdateSecurityReviewObservationMutation,
} from '@globals/api-sdk/queries';
import type { VendorSecurityReviewObservationRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { closeConfirmationModal } from '@helpers/temp-confirmation-modal';
import { sharedVendorsSecurityReviewObservationsController } from './vendors-security-review-observations-controller';

class VendorsSecurityReviewObservationsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    createObservationMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerCreateSecurityReviewObservationMutation,
    );

    updateObservationMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerUpdateSecurityReviewObservationMutation,
    );

    deleteObservationMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerDeleteSecurityReviewObservationMutation,
    );

    get isCreating(): boolean {
        return this.createObservationMutation.isPending;
    }

    get isUpdating(): boolean {
        return this.updateObservationMutation.isPending;
    }

    get isDeleting(): boolean {
        return this.deleteObservationMutation.isPending;
    }

    createObservation = (
        securityReviewId: number,
        observationData: VendorSecurityReviewObservationRequestDto,
        onSuccess?: () => void,
    ) => {
        if (!securityReviewId || this.createObservationMutation.isPending) {
            return;
        }

        this.createObservationMutation.mutate({
            path: { id: securityReviewId },
            body: observationData,
        });

        when(
            () => !this.createObservationMutation.isPending,
            () => {
                if (this.createObservationMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'observation-create-error',
                        props: {
                            title: t`Error creating observation`,
                            description: t`An error occurred while creating the observation. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        hasTimeout: true,
                        id: 'observation-create-success',
                        props: {
                            title: t`Observation created`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedVendorsSecurityReviewObservationsController.securityReviewObservationsQuery.invalidate();

                    onSuccess?.();
                }
            },
        );
    };

    updateObservation = (
        observationId: number,
        observationData: VendorSecurityReviewObservationRequestDto,
    ) => {
        if (!observationId || this.updateObservationMutation.isPending) {
            return;
        }

        this.updateObservationMutation.mutate({
            path: { id: observationId },
            body: observationData,
        });

        when(
            () => !this.updateObservationMutation.isPending,
            () => {
                if (this.updateObservationMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'observation-update-error',
                        props: {
                            title: t`Error updating observation`,
                            description: t`An error occurred while updating the observation. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        hasTimeout: true,
                        id: 'observation-update-success',
                        props: {
                            title: t`Observation updated`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedVendorsSecurityReviewObservationsController.securityReviewObservationsQuery.invalidate();
                }
            },
        );
    };

    deleteObservation = (observationId: number) => {
        if (!observationId || this.deleteObservationMutation.isPending) {
            return;
        }

        this.deleteObservationMutation.mutate({
            path: { id: observationId },
        });

        when(
            () => !this.deleteObservationMutation.isPending,
            () => {
                if (this.deleteObservationMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'observation-delete-error',
                        props: {
                            title: t`Error deleting observation`,
                            description: t`An error occurred while deleting the observation. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        hasTimeout: true,
                        id: 'observation-delete-success',
                        props: {
                            title: t`Observation deleted`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedVendorsSecurityReviewObservationsController.securityReviewObservationsQuery.invalidate();
                }

                closeConfirmationModal();
            },
        );
    };
}

export const sharedVendorsSecurityReviewObservationsMutationController =
    new VendorsSecurityReviewObservationsMutationController();
