import { isEmpty, isNil } from 'lodash-es';
import { sharedLinkControlsController } from '@controllers/evidence-library';
import { panelController } from '@controllers/panel';
import type { CreateRiskCustomMutationType } from '@controllers/risk';
import { snackbarController } from '@controllers/snackbar';
import { sharedVendorsProfileRisksController } from '@controllers/vendors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    riskManagementControllerUploadDocumentsMutation,
    vendorsRiskManagementControllerCreateCustomRiskMutation,
    vendorsRiskManagementControllerDeleteCustomRiskMutation,
    vendorsRiskManagementControllerUpdateRiskPartiallyMutation,
} from '@globals/api-sdk/queries';
import type {
    AuditFrameworkControlResponseDto,
    RiskRequestDto,
    RiskResponseDto,
} from '@globals/api-sdk/types';
import { zRiskRequestDto } from '@globals/api-sdk/zod';
import {
    makeAutoObservable,
    ObservedMutation,
    toJS,
    when,
} from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { closeConfirmationModal } from '@helpers/temp-confirmation-modal';
import type { FormValues } from '@ui/forms';
import {
    getAdaptedControls,
    getInherentValue,
    getResidualScore,
    getResidualValue,
    getScore,
    getUniqueIdsList,
} from './helpers/vendors-risks-adapter.helper';
import type { VendorRiskUploadStatus } from './types/vendor-risk.type';
import { sharedVendorsDetailsController } from './vendors-details-controller';
import { sharedVendorsRisksController } from './vendors-risks-controller';

class VendorsRisksMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    risk: RiskResponseDto | null = null;
    uploadStatus: VendorRiskUploadStatus = 'NOT_STARTED';
    mutatedRiskDetails: RiskRequestDto | null = null;
    mappedControls: ListBoxItemData[] = [];
    createRiskWizardData: CreateRiskCustomMutationType | null = null;

    riskCreateMutation = new ObservedMutation(
        vendorsRiskManagementControllerCreateCustomRiskMutation,
    );

    riskUpdateMutation = new ObservedMutation(
        vendorsRiskManagementControllerUpdateRiskPartiallyMutation,
    );

    vendorRiskUploadDocumentMutation = new ObservedMutation(
        riskManagementControllerUploadDocumentsMutation,
    );

    deleteRiskMutation = new ObservedMutation(
        vendorsRiskManagementControllerDeleteCustomRiskMutation,
    );

    setCreateRiskWizardDataOverride = (data: FormValues) => {
        this.createRiskWizardData = {
            ...this.createRiskWizardData,
            ...data,
        };
    };

    getMutatedStateForStep = (
        stepName: string,
    ): Partial<CreateRiskCustomMutationType> => {
        if (!this.createRiskWizardData) {
            return {};
        }

        switch (stepName) {
            case 'sourceAndStatus': {
                return {
                    riskSourceGroup: this.createRiskWizardData.riskSourceGroup,
                    statusGroup: this.createRiskWizardData.statusGroup,
                };
            }
            case 'details': {
                return {
                    riskDetailsGroup:
                        this.createRiskWizardData.riskDetailsGroup,
                };
            }
            case 'assessmentAndTreatment': {
                return {
                    inherentScoreGroup:
                        this.createRiskWizardData.inherentScoreGroup,
                    treatmentGroup: this.createRiskWizardData.treatmentGroup,
                };
            }
            default: {
                return {};
            }
        }
    };

    setMappedControls = (controls: ListBoxItemData[]) => {
        this.mappedControls = controls;
    };

    resetProcess = () => {
        this.risk = null;
        this.uploadStatus = 'NOT_STARTED';
        this.mutatedRiskDetails = null;
        this.createRiskWizardData = null;
    };

    createMutatedRiskDetails = (vendorId: number) => {
        const formValues = toJS(this.createRiskWizardData) ?? {};
        const status = formValues.statusGroup?.status.value ?? 'ACTIVE';
        const controls = getAdaptedControls(
            sharedLinkControlsController.selectedControls as AuditFrameworkControlResponseDto[],
        );
        const identifiedAt = formValues.riskDetailsGroup?.identifiedAt
            ? formatDate('timestamp', formValues.riskDetailsGroup.identifiedAt)
            : undefined;
        const categories = formValues.riskDetailsGroup?.categories
            ? getUniqueIdsList(formValues.riskDetailsGroup.categories)
            : undefined;
        const owners = formValues.riskDetailsGroup?.owners
            ? getUniqueIdsList(formValues.riskDetailsGroup.owners)
            : undefined;
        const impact = getInherentValue(formValues, 'impact') ?? undefined;
        const likelihood =
            getInherentValue(formValues, 'likelihood') ?? undefined;
        const score = getScore(formValues) ?? undefined;
        const residualScore = formValues.treatmentGroup
            ? (getResidualScore(formValues.treatmentGroup) ?? undefined)
            : undefined;
        const residualImpact = formValues.treatmentGroup
            ? (getResidualValue(formValues.treatmentGroup, 'residualImpact') ??
              undefined)
            : undefined;
        const residualLikelihood = formValues.treatmentGroup
            ? (getResidualValue(
                  formValues.treatmentGroup,
                  'residualLikelihood',
              ) ?? undefined)
            : undefined;
        const treatmentPlan =
            formValues.treatmentGroup?.treatment?.value ?? 'UNTREATED';
        const treatmentDetails =
            formValues.treatmentGroup?.treatmentPlan ?? undefined;
        const reviewers = formValues.treatmentGroup?.reviewers
            ? getUniqueIdsList(formValues.treatmentGroup.reviewers)
            : undefined;
        const anticipatedCompletionDate = formValues.treatmentGroup
            ?.anticipatedCompletionDate
            ? formatDate(
                  'timestamp',
                  formValues.treatmentGroup.anticipatedCompletionDate,
              )
            : undefined;
        const completionDate = formValues.treatmentGroup?.completedDate
            ? formatDate('timestamp', formValues.treatmentGroup.completedDate)
            : undefined;

        this.mutatedRiskDetails = zRiskRequestDto.parse({
            type: 'EXTERNAL',
            status,
            identifiedAt,
            vendorId,
            categories,
            owners,
            impact,
            likelihood,
            score,
            residualScore,
            residualImpact,
            residualLikelihood,
            treatmentPlan,
            treatmentDetails,
            controls,
            title: formValues.riskDetailsGroup?.title,
            description: formValues.riskDetailsGroup?.description,
            reviewers,
            anticipatedCompletionDate,
            completionDate,
        });
    };

    createVendorRisk = (vendorId: number) => {
        if (this.uploadStatus === 'IN_PROGRESS') {
            return;
        }
        this.uploadStatus = 'IN_PROGRESS';

        this.createMutatedRiskDetails(vendorId);

        if (isNil(this.risk)) {
            this.riskCreateMutation.mutate({
                body: {
                    ...toJS(this.mutatedRiskDetails),
                    vendorId,
                    title: this.mutatedRiskDetails?.title || '',
                    description: this.mutatedRiskDetails?.description || '',
                    treatmentPlan:
                        this.mutatedRiskDetails?.treatmentPlan || 'UNTREATED',
                },
            });
        } else {
            this.updateVendorRisk(this.risk.riskId);
        }

        when(
            () => !this.riskCreateMutation.isPending,
            () => {
                if (this.riskCreateMutation.hasError) {
                    this.uploadStatus = 'ERROR';

                    snackbarController.addSnackbar({
                        id: 'vendor-risk-upload-error',
                        props: {
                            title: 'Error creating vendor risk data',
                            description:
                                'An error occurred while creating the risk data. Try again later.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                const { response } = this.riskCreateMutation;

                this.risk = response;

                this.mayUploadRiskFiles(response);
            },
        );

        when(
            () => this.uploadStatus === 'SUCCESS',
            () => {
                snackbarController.addSnackbar({
                    id: 'vendor-risk-upload-success',
                    props: {
                        title: 'Risk created',
                        description: 'The risk was created successfully.',
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };

    updateVendorRisk = (riskId: string) => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (isNil(vendorDetails)) {
            console.error('Invalid data provided', vendorDetails);

            return;
        }

        this.uploadStatus = 'IN_PROGRESS';
        this.createMutatedRiskDetails(vendorDetails.id);

        this.riskUpdateMutation.mutate({
            path: { risk_id: riskId },
            body: {
                ...toJS(this.mutatedRiskDetails),
                title: this.mutatedRiskDetails?.title || '',
                description: this.mutatedRiskDetails?.description || '',
                treatmentPlan:
                    this.mutatedRiskDetails?.treatmentPlan || 'UNTREATED',
            },
        });

        when(
            () => !this.riskUpdateMutation.isPending,
            () => {
                if (this.riskUpdateMutation.hasError) {
                    this.uploadStatus = 'ERROR';

                    snackbarController.addSnackbar({
                        id: 'vendor-risk-upload-error',
                        props: {
                            title: 'Error updating vendor risk data',
                            description:
                                'An error occurred while updating the risk data. Try again later.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                sharedVendorsRisksController.risksQuery.invalidate();

                const { response } = this.riskUpdateMutation;

                this.risk = response;

                this.mayUploadRiskFiles(response);
            },
        );
    };

    mayUploadRiskFiles = (response: RiskResponseDto | null) => {
        const formValues = toJS(this.createRiskWizardData) ?? {};

        if (
            !isEmpty(formValues.riskDetailsGroup?.documents) &&
            response?.riskId
        ) {
            if (Array.isArray(formValues.riskDetailsGroup?.documents)) {
                this.uploadRiskFiles(
                    response.riskId,
                    formValues.riskDetailsGroup.documents as unknown as File[],
                );
            }
        } else {
            this.uploadStatus = 'SUCCESS';
        }
    };

    uploadRiskFiles = (riskId: string, files: File[]) => {
        this.vendorRiskUploadDocumentMutation.mutate({
            body: {
                files,
            },
            path: { risk_id: riskId },
        });

        when(
            () => !this.vendorRiskUploadDocumentMutation.isPending,
            () => {
                if (this.vendorRiskUploadDocumentMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-risk-upload-error',
                        props: {
                            title: 'Error uploading risk supporting documents',
                            description:
                                'An error occurred while uploading the supporting documents. Try again later.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }
                // Setting status to success because the Risk was created in spite of the error
                if (this.uploadStatus !== 'SUCCESS') {
                    this.uploadStatus = 'SUCCESS';
                }
                sharedVendorsRisksController.risksQuery.invalidate();
            },
        );
    };

    deleteRisk = (riskId: string) => {
        if (isEmpty(riskId)) {
            return;
        }

        this.deleteRiskMutation.mutate({
            body: { risksIds: [riskId] },
        });

        when(
            () => !this.deleteRiskMutation.isPending,
            () => {
                if (this.deleteRiskMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-risk-delete-error',
                        props: {
                            title: 'Error deleting risk',
                            description:
                                'An error occurred while deleting the risk. Try again later.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'vendor-risk-delete-success',
                        props: {
                            title: 'Risk deleted',
                            description: 'The risk was deleted successfully.',
                            severity: 'success',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }

                // Invalidate both queries because sometimes the panel is active for a vendor profile
                sharedVendorsProfileRisksController.allVendorsRisksQuery.invalidate();
                sharedVendorsRisksController.risksQuery.invalidate();
                sharedVendorsDetailsController.vendorDetailsQuery.invalidate();
                sharedVendorsRisksController.riskDashboardQuery.invalidate();

                closeConfirmationModal();
                panelController.closePanel();
            },
        );
    };
}

export const sharedVendorsRisksMutationController =
    new VendorsRisksMutationController();
