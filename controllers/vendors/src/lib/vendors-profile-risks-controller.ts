import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { vendorsRiskManagementControllerGetVendorRisksListOptions } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import {
    COLUMN_NAMES_TO_SORT_IDS_MAP,
    DEFAULT_SORT_COLUMN,
    DEFAULT_SORT_ORDER,
} from './constants/vendors-profile-risks-controller.constants';
import { adaptRiskScoreSeverity } from './helpers/vendors-risks-adapter.helper';
import type { VendorsRisksTableItem } from './types/vendor-risk.type';
import { sharedVendorsRisksController } from './vendors-risks-controller';

class VendorsProfileRisksController {
    vendorId?: number;
    hasSearchFilter = false;

    constructor() {
        makeAutoObservable(this);
    }

    allVendorsRisksQuery = new ObservedQuery(
        vendorsRiskManagementControllerGetVendorRisksListOptions,
    );

    get allVendorsRisks(): VendorsRisksTableItem[] {
        return (this.allVendorsRisksQuery.data?.data ?? []).map((risk) =>
            adaptRiskScoreSeverity(
                risk,
                sharedVendorsRisksController.settings?.thresholds ?? [],
            ),
        );
    }

    get isLoading(): boolean {
        return this.allVendorsRisksQuery.isLoading;
    }

    get currentVendorId(): number | undefined {
        return this.vendorId;
    }

    get total(): number {
        return this.allVendorsRisksQuery.data?.total ?? 0;
    }

    setCurrentVendorId(id: number | undefined) {
        this.vendorId = id;
    }

    loadVendorsRisks = (params: FetchDataResponseParams) => {
        if (!this.vendorId) {
            throw new Error('Vendor ID is required');
        }
        this.hasSearchFilter = !isEmpty(params.globalFilter.search);
        when(
            () => !sharedVendorsRisksController.riskSettingsQuery.isLoading,
            () => {
                if (!this.vendorId) {
                    throw new Error('Vendor ID is required');
                }
                const { pagination, globalFilter, sorting } = params;
                const { page, pageSize } = pagination;
                const { search } = globalFilter;

                type Query = Required<
                    Parameters<
                        typeof vendorsRiskManagementControllerGetVendorRisksListOptions
                    >
                >[0]['query'];

                const query: Query = {
                    page,
                    limit: pageSize,
                    q: search ?? '',
                    vendorId: this.vendorId,
                    sort: DEFAULT_SORT_COLUMN,
                    sortDir: DEFAULT_SORT_ORDER,
                };

                if (!isEmpty(sorting)) {
                    // ToDo: https://drata.atlassian.net/browse/ENG-65657 -- when datatable callback generics are supported, `as` can be removed.
                    const sortId = sorting[0].id;

                    if (sortId in COLUMN_NAMES_TO_SORT_IDS_MAP) {
                        const mappedSort =
                            COLUMN_NAMES_TO_SORT_IDS_MAP[
                                sortId as keyof typeof COLUMN_NAMES_TO_SORT_IDS_MAP
                            ];

                        // ToDo: https://drata.atlassian.net/browse/ENG-65657 -- when datatable callback generics are supported, `as` can be removed.
                        query.sort = mappedSort as 'IDENTIFIED_DATE';
                        query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
                    }
                    query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
                }

                this.allVendorsRisksQuery.load({
                    query,
                });
            },
        );
    };

    loadInitialData = () => {
        this.allVendorsRisksQuery.load({
            query: {
                page: 1,
                limit: 10,
                q: '',
                sort: DEFAULT_SORT_COLUMN,
                sortDir: DEFAULT_SORT_ORDER,
                vendorId: this.vendorId,
            },
        });
    };
}

export const sharedVendorsProfileRisksController =
    new VendorsProfileRisksController();
