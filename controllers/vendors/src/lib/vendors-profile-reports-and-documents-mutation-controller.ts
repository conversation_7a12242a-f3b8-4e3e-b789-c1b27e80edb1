import { isNil, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    questionnairesControllerGetVendorQuestionnaireArchiveOptions,
    vendorsDocumentsControllerDeleteVendorDocumentMutation,
    vendorsDocumentsControllerGetVendorDocumentDownloadUrlOptions,
    vendorsDocumentsControllerUploadVendorDocumentMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import {
    downloadFileFromSignedUrl,
    downloadFileFromSignedUrlInCurrentWindow,
} from '@helpers/download-file';
import { VENDORS_CURRENT_DEFAULT_DOCUMENT_TYPE } from './constants/vendors-current-controller.constants';
import { sharedVendorsDetailsController } from './vendors-details-controller';
import { sharedVendorsProfileReportsAndDocumentsController } from './vendors-profile-reports-and-documents-controller';

export class VendorsProfileReportsAndDocumentsMutationController {
    uploadInProgress = false;
    activeUploads = 0;

    constructor() {
        makeAutoObservable(this);
    }

    uploadFileQuery = new ObservedMutation(
        vendorsDocumentsControllerUploadVendorDocumentMutation,
        {
            onSuccess: () => {
                sharedVendorsProfileReportsAndDocumentsController.paginatedDocuments.invalidate();
            },
        },
    );

    deleteFileQuery = new ObservedMutation(
        vendorsDocumentsControllerDeleteVendorDocumentMutation,
        {
            onSuccess: () => {
                sharedVendorsProfileReportsAndDocumentsController.paginatedDocuments.invalidate();
            },
        },
    );

    downloadUrlQuery = new ObservedQuery(
        vendorsDocumentsControllerGetVendorDocumentDownloadUrlOptions,
    );

    questionnaireDownloadQuery = new ObservedQuery(
        questionnairesControllerGetVendorQuestionnaireArchiveOptions,
    );

    get isUploadPending(): boolean {
        return this.activeUploads > 0 || this.uploadFileQuery.isPending;
    }

    get isDeletePending(): boolean {
        return this.deleteFileQuery.isPending;
    }

    get isDownloadPending(): boolean {
        return this.downloadUrlQuery.isLoading;
    }

    get isQuestionnaireDownloadPending(): boolean {
        return this.questionnaireDownloadQuery.isLoading;
    }

    uploadFile = async (data: FormData): Promise<void> => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (isNil(data) || !vendorDetails?.id) {
            return;
        }

        this.activeUploads = this.activeUploads + 1;

        try {
            await this.uploadFileQuery.mutateAsync({
                body: {
                    file: data.get('file') as File,
                    type: VENDORS_CURRENT_DEFAULT_DOCUMENT_TYPE,
                },
                path: { id: vendorDetails.id },
            });
        } catch (error) {
            console.error('Failed to upload file:', error);
        } finally {
            this.activeUploads = this.activeUploads - 1;
        }
    };

    deleteFile = async (data: FormData): Promise<void> => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (isNil(data) || !vendorDetails?.id) {
            return Promise.resolve();
        }

        try {
            await this.deleteFileQuery.mutateAsync({
                path: {
                    id: vendorDetails.id,
                    docId: Number(data.get('id')),
                },
            });
        } catch (error) {
            console.error('Failed to delete file:', error);
        }
    };

    downloadFile = (documentId: number): void => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id || !documentId) {
            return;
        }

        this.downloadUrlQuery.load({
            path: { id: vendorDetails.id, docId: documentId },
        });

        when(
            () => !this.downloadUrlQuery.isLoading,
            () => {
                const downloadData = this.downloadUrlQuery.data;

                if (this.downloadUrlQuery.error) {
                    console.error(
                        'Failed to download file:',
                        this.downloadUrlQuery.error,
                    );
                    snackbarController.addSnackbar({
                        id: `document-download-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to download document`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (downloadData?.signedUrl) {
                    downloadFileFromSignedUrl(downloadData.signedUrl);

                    snackbarController.addSnackbar({
                        id: `document-download-success-${uniqueId()}`,
                        hasTimeout: true,
                        props: {
                            title: t`Document download started`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `document-download-error-${uniqueId()}`,
                    props: {
                        title: t`Unable to get download URL`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    downloadQuestionnaire = (responseId: number): void => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id || !responseId) {
            return;
        }

        this.questionnaireDownloadQuery.load({
            path: { vendorId: vendorDetails.id, responseId },
        });

        when(
            () => !this.questionnaireDownloadQuery.isLoading,
            () => {
                const downloadData = this.questionnaireDownloadQuery.data;

                if (this.questionnaireDownloadQuery.error) {
                    console.error(
                        'Failed to download questionnaire:',
                        this.questionnaireDownloadQuery.error,
                    );
                    snackbarController.addSnackbar({
                        id: `questionnaire-download-error-${uniqueId()}`,
                        props: {
                            title: t`Unable to download questionnaire data`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (downloadData?.signedUrl) {
                    downloadFileFromSignedUrlInCurrentWindow(
                        downloadData.signedUrl,
                    );
                    snackbarController.addSnackbar({
                        id: `questionnaire-download-success-${uniqueId()}`,
                        hasTimeout: true,
                        props: {
                            title: t`The questionnaire is being downloaded`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `questionnaire-download-error-${uniqueId()}`,
                    props: {
                        title: t`Unable to get download URL`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedVendorsProfileReportsAndDocumentsMutationController =
    new VendorsProfileReportsAndDocumentsMutationController();
