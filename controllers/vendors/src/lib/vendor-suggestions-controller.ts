import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import {
    vendorsDiscoveryControllerDeleteVendorDiscoveredMutation,
    vendorsDiscoveryControllerDeleteVendorsDiscoveredBulkMutation,
    vendorsDiscoveryControllerGetVendorsDiscoveredOptions,
    vendorsDiscoveryControllerVendorDiscoveredConvertMutation,
    vendorsDiscoveryControllerVendorsDiscoveredBulkActionsMutation,
} from '@globals/api-sdk/queries';
import type { VendorDiscoveredResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';

export type SuggestedVendor = VendorDiscoveredResponseDto;

class VendorSuggestionsController {
    constructor() {
        makeAutoObservable(this);
    }

    suggestedVendorsQuery = new ObservedQuery(
        vendorsDiscoveryControllerGetVendorsDiscoveredOptions,
    );

    addVendorMutation = new ObservedMutation(
        vendorsDiscoveryControllerVendorDiscoveredConvertMutation,
    );

    dismissVendorMutation = new ObservedMutation(
        vendorsDiscoveryControllerDeleteVendorDiscoveredMutation,
    );

    addAllVendorsMutation = new ObservedMutation(
        vendorsDiscoveryControllerVendorsDiscoveredBulkActionsMutation,
    );

    dismissAllVendorsMutation = new ObservedMutation(
        vendorsDiscoveryControllerDeleteVendorsDiscoveredBulkMutation,
    );

    get suggestedVendors(): SuggestedVendor[] {
        return this.suggestedVendorsQuery.data?.data ?? [];
    }

    get totalSuggestions(): number {
        return this.suggestedVendorsQuery.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return this.suggestedVendorsQuery.isLoading;
    }

    get error(): string | null {
        return this.suggestedVendorsQuery.error?.message ?? null;
    }

    get hasSuggestions(): boolean {
        return !isEmpty(this.suggestedVendors);
    }

    loadSuggestedVendors = (options?: { page?: number; limit?: number }) => {
        this.suggestedVendorsQuery.load({
            query: {
                page: options?.page ?? DEFAULT_PAGE,
                limit: options?.limit ?? DEFAULT_PAGE_SIZE,
            },
        });
    };

    addVendor = (vendorId: number) => {
        this.addVendorMutation.mutate({
            path: { id: vendorId },
            body: {
                vendorDiscoveredAction: 'ADD',
            },
        });

        when(
            () => !this.addVendorMutation.isPending,
            () => {
                if (this.addVendorMutation.error) {
                    snackbarController.addSnackbar({
                        id: 'add-vendor-error',
                        props: {
                            severity: 'critical',
                            title: t`Failed to add vendor`,
                            description: t`There was an error adding the vendor. Please try again.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'add-vendor-success',
                        hasTimeout: true,
                        props: {
                            severity: 'success',
                            title: t`Vendor added successfully`,
                            description: t`The vendor has been added to your organization.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    this.loadSuggestedVendors();
                }
            },
        );
    };

    dismissVendor = (vendorId: number) => {
        this.dismissVendorMutation.mutate({
            path: { id: vendorId },
        });

        when(
            () => !this.dismissVendorMutation.isPending,
            () => {
                if (this.dismissVendorMutation.error) {
                    snackbarController.addSnackbar({
                        id: 'dismiss-vendor-error',
                        props: {
                            severity: 'critical',
                            title: t`Failed to ignore vendor`,
                            description: t`There was an error ignoring the vendor. Please try again.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'dismiss-vendor-success',
                        hasTimeout: true,
                        props: {
                            severity: 'success',
                            title: t`Vendor ignored successfully`,
                            description: t`The vendor suggestion has been ignored.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    this.loadSuggestedVendors();
                }
            },
        );
    };

    addAllVendors = () => {
        const vendorIds = this.suggestedVendors.map((vendor) => vendor.id);
        const vendorCount = vendorIds.length;

        this.addAllVendorsMutation.mutate({
            body: {
                vendorDiscoveredIds: vendorIds,
                vendorDiscoveredAction: 'ADD',
            },
        });

        when(
            () => !this.addAllVendorsMutation.isPending,
            () => {
                if (this.addAllVendorsMutation.error) {
                    snackbarController.addSnackbar({
                        id: 'add-all-vendors-error',
                        props: {
                            severity: 'critical',
                            title: t`Failed to add all vendors`,
                            description: t`There was an error adding the vendors. Please try again.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'add-all-vendors-success',
                        hasTimeout: true,
                        props: {
                            severity: 'success',
                            title: t`All vendors added successfully`,
                            description: t`${vendorCount} vendors have been added to your organization.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    this.loadSuggestedVendors();
                }
            },
        );
    };

    dismissAllVendors = () => {
        const vendorIds = this.suggestedVendors.map((vendor) => vendor.id);
        const vendorCount = vendorIds.length;

        this.dismissAllVendorsMutation.mutate({
            body: {
                vendorDiscoveredIds: vendorIds,
            },
        });

        when(
            () => !this.dismissAllVendorsMutation.isPending,
            () => {
                if (this.dismissAllVendorsMutation.error) {
                    snackbarController.addSnackbar({
                        id: 'dismiss-all-vendors-error',
                        props: {
                            severity: 'critical',
                            title: t`Failed to ignore all vendors`,
                            description: t`There was an error ignoring the vendors. Please try again.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'dismiss-all-vendors-success',
                        hasTimeout: true,
                        props: {
                            severity: 'success',
                            title: t`All vendors ignored successfully`,
                            description: t`${vendorCount} vendor suggestions have been ignored.`,
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    this.loadSuggestedVendors();
                }
            },
        );
    };
}

export const sharedVendorSuggestionsController =
    new VendorSuggestionsController();
