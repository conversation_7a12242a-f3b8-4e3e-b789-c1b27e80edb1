import { isEmpty } from 'lodash-es';
import { vendorsSecurityReviewsControllerGetSecurityReviewObservationsOptions } from '@globals/api-sdk/queries';
import type {
    UserResponseDto,
    VendorSecurityReviewObservationResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedVendorsRisksMutationController } from './vendors-risks-mutation-controller';

export interface TransformedVendorSecurityReviewObservation {
    id: string;
    observation: string;
    source: string;
    createdAt: string;
    updatedAt: string;
    user: UserResponseDto | null;
    owner: {
        id: number | undefined;
        firstName: string | undefined;
        lastName: string | undefined;
        avatarUrl: string | null | undefined;
        email: string | undefined;
        entryId: string | undefined;
        jobTitle: string | null | undefined;
        drataTermsAgreedAt: string | null | undefined;
    };
}

class VendorsSecurityReviewObservationsController {
    constructor() {
        makeAutoObservable(this);
    }

    securityReviewObservationsQuery = new ObservedQuery(
        vendorsSecurityReviewsControllerGetSecurityReviewObservationsOptions,
    );

    get isLoading(): boolean {
        return this.securityReviewObservationsQuery.isLoading;
    }

    get securityReviewObservations():
        | VendorSecurityReviewObservationResponseDto[]
        | null {
        return this.securityReviewObservationsQuery.data?.data ?? null;
    }

    get observations(): TransformedVendorSecurityReviewObservation[] {
        return (this.securityReviewObservations ?? []).map(
            (observation): TransformedVendorSecurityReviewObservation => {
                return {
                    ...observation,
                    id: observation.id.toString(),
                    observation: observation.observation,
                    source: observation.source,
                    owner: {
                        id: observation.user?.id,
                        firstName: observation.user?.firstName,
                        lastName: observation.user?.lastName,
                        avatarUrl: observation.user?.avatarUrl,
                        email: observation.user?.email,
                        entryId: observation.user?.entryId,
                        jobTitle: observation.user?.jobTitle,
                        drataTermsAgreedAt:
                            observation.user?.drataTermsAgreedAt,
                    },
                };
            },
        );
    }

    /**
     * TODO: Improve this method to handle pagination properly, endpoints changes will be required.
     */
    getObservationById(
        id: number,
    ): VendorSecurityReviewObservationResponseDto | null {
        const observations = this.securityReviewObservations;

        if (!observations) {
            return null;
        }

        return (
            observations.find((observation) => observation.id === id) ?? null
        );
    }

    get(): boolean {
        return !isEmpty(this.securityReviewObservations);
    }

    /**
     * TODO: implement infinite query after https://drata.atlassian.net/browse/ENG-66498.
     */
    loadSecurityReviewObservations = (
        options: Parameters<
            typeof this.securityReviewObservationsQuery.load
        >[0],
    ) => {
        this.securityReviewObservationsQuery.load(options);
    };

    preloadRiskWizardWithObservation = (observationId: number) => {
        sharedVendorsRisksMutationController.resetProcess();

        when(
            () => !this.isLoading && this.securityReviewObservations !== null,
            () => {
                const targetObservation =
                    this.getObservationById(observationId);

                if (!targetObservation) {
                    // TODO: Redirect to appropriate page when observation is not found.
                    throw new Error(
                        `Observation with ID ${observationId} not found.`,
                    );
                }

                // Concatenate observation and source for the description field
                const observationText = targetObservation.observation;
                const sourceText = targetObservation.source
                    ? ` ${targetObservation.source}`
                    : '';
                const description = `${observationText}${sourceText}`.trim();

                // Preload the form with the observation data
                sharedVendorsRisksMutationController.setCreateRiskWizardDataOverride(
                    {
                        riskDetailsGroup: {
                            description,
                        },
                    },
                );
            },
        );
    };
}

export const sharedVendorsSecurityReviewObservationsController =
    new VendorsSecurityReviewObservationsController();
