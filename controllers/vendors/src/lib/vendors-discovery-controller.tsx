import { isEmpty, isNil } from 'lodash-es';
import type { VendorDiscoveryItem } from '@components/vendors-prospective-add';
import {
    MAX_SEARCH_CHARACTERS,
    MIN_SEARCH_CHARACTERS,
} from '@controllers/vendors';
import { Avatar } from '@cosmos/components/avatar';
import type {
    ListBoxGroupData,
    ListBoxItemData,
} from '@cosmos/components/list-box';
import { vendorsDiscoveryControllerGetVendorsDiscoveryOptions } from '@globals/api-sdk/queries';
import type { VendorDiscoveryResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { generateLogoDevUrl } from '@helpers/formatters';
import {
    generateVendorInitials,
    truncateText,
} from './helpers/vendor-discovery.helper';

export class VendorsDiscoveryController {
    constructor() {
        makeAutoObservable(this);
    }

    allVendorsDiscoveredQuery = new ObservedQuery(
        vendorsDiscoveryControllerGetVendorsDiscoveryOptions,
    );

    itemToString = (
        selectedItem: ListBoxItemData | ListBoxGroupData | null,
    ): string => {
        const isValidItem = selectedItem && !('items' in selectedItem);

        if (isValidItem) {
            const listBoxItem = selectedItem;

            return listBoxItem.value || listBoxItem.label || '';
        }

        return '';
    };

    transformSelectedItem = (
        selectedItem: ListBoxItemData | null,
    ): ListBoxItemData | null => {
        const isInvalidItem = !selectedItem || 'items' in selectedItem;

        if (isInvalidItem) {
            return selectedItem;
        }

        const listBoxItem = selectedItem;

        return {
            ...listBoxItem,
            label: listBoxItem.label,
            description: undefined,
        };
    };

    get vendorsDiscovery(): VendorDiscoveryItem[] {
        const responseData = this.allVendorsDiscoveredQuery.data;

        if (!Array.isArray(responseData?.data)) {
            return [];
        }

        return responseData.data
            .filter(
                (
                    vendor,
                ): vendor is VendorDiscoveryResponseDto & {
                    name: string;
                    product_name: string;
                } => !isNil(vendor.name) && !isNil(vendor.product_name),
            )
            .map(
                (vendor): VendorDiscoveryItem => ({
                    label: truncateText(
                        `${vendor.name} | ${vendor.product_name}`,
                    ),
                    value: vendor.name,
                    id: truncateText(vendor.name),
                    description: truncateText(vendor.website_url || ''),
                    startSlot: (
                        <Avatar
                            fallbackText={generateVendorInitials(vendor.name)}
                            imgAlt={`${vendor.name} logo`}
                            size="xs"
                            imgSrc={
                                vendor.favicon ||
                                (vendor.domain
                                    ? generateLogoDevUrl(vendor.domain)
                                    : '')
                            }
                        />
                    ),
                }),
            );
    }

    get isLoading(): boolean {
        return this.allVendorsDiscoveredQuery.isLoading;
    }

    get hasError(): boolean {
        return this.allVendorsDiscoveredQuery.hasError;
    }

    get error(): Error | null {
        return this.allVendorsDiscoveredQuery.error;
    }

    loadVendorsDiscovery = ({ search }: { search?: string } = {}): void => {
        const trimmedSearch = search?.trim() || '';

        if (
            !isEmpty(trimmedSearch) &&
            trimmedSearch.length < MIN_SEARCH_CHARACTERS
        ) {
            return;
        }

        this.allVendorsDiscoveredQuery.load({
            query: {
                q: search?.slice(0, MAX_SEARCH_CHARACTERS),
            },
        });
    };
}

export const sharedVendorsDiscoveryController =
    new VendorsDiscoveryController();
