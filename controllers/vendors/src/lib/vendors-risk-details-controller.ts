import { vendorsRiskManagementControllerGetRiskOptions } from '@globals/api-sdk/queries';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class VendorsRiskDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorRiskQuery = new ObservedQuery(
        vendorsRiskManagementControllerGetRiskOptions,
    );

    get vendorRiskDetail(): RiskWithCustomFieldsResponseDto | null {
        return this.vendorRiskQuery.data
            ? ({
                  ...this.vendorRiskQuery.data,
                  customFields: [],
              } as RiskWithCustomFieldsResponseDto)
            : null;
    }

    get isLoading(): boolean {
        return this.vendorRiskQuery.isLoading;
    }

    loadVendorRisk = (riskId: string) => {
        if (!riskId) {
            return;
        }

        this.vendorRiskQuery.load({
            path: {
                risk_id: riskId,
            },
        });
    };
}

export const sharedVendorsRiskDetailsController =
    new VendorsRiskDetailsController();
