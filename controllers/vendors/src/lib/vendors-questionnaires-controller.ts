import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import { DEFAULT_PAGE } from '@cosmos/components/datatable';
import {
    questionnairesControllerDeleteQuestionnaireSentMutation,
    questionnairesControllerGetQuestionnaireAnswerDownloadOptions,
    questionnairesControllerGetQuestionnaireAnswersInfiniteOptions,
    vendorsQuestionnairesControllerListVendorQuestionnairesOptions,
} from '@globals/api-sdk/queries';
import type {
    QuestionnaireAnswersResponseDto,
    QuestionnaireSentResponseDto,
    QuestionnairesSentResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { downloadBlob } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { DEFAULT_LIMIT } from './constants/vendors-questionnaires-controller.constants';
import { sharedVendorsProfileReportsAndDocumentsController } from './vendors-profile-reports-and-documents-controller';

class VendorsQuestionnairesController {
    lastSearchQuery = '';

    constructor() {
        makeAutoObservable(this);
    }

    questionnaireId: number | null = null;

    currentQuery: string | null = null;

    listVendorQuestionnairesQuery = new ObservedQuery(
        vendorsQuestionnairesControllerListVendorQuestionnairesOptions,
    );

    getQuestionnaireAnswersQuery = new ObservedInfiniteQuery(
        questionnairesControllerGetQuestionnaireAnswersInfiniteOptions,
    );

    getDownloadAnswerFileQuery = new ObservedQuery(
        questionnairesControllerGetQuestionnaireAnswerDownloadOptions,
    );

    deleteQuestionnaireMutation = new ObservedMutation(
        questionnairesControllerDeleteQuestionnaireSentMutation,
        {
            onSuccess: () => {
                sharedVendorsProfileReportsAndDocumentsController.paginatedQuestionnaires.invalidate();
            },
        },
    );

    get allVendorsQuestionnaires(): QuestionnairesSentResponseDto | null {
        return this.listVendorQuestionnairesQuery.data;
    }

    get isLoading(): boolean {
        return this.listVendorQuestionnairesQuery.isLoading;
    }

    get isGetAnswersLoading(): boolean {
        return this.getQuestionnaireAnswersQuery.isLoading;
    }

    get firstVendorQuestionnaire(): QuestionnaireSentResponseDto | null {
        return this.allVendorsQuestionnaires?.data[0] ?? null;
    }

    get questionnaireAnswers(): QuestionnaireAnswersResponseDto['data'] {
        return (
            this.getQuestionnaireAnswersQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    get firstQuestionnaireAnswersData(): {
        total: number;
        answered: number;
    } | null {
        if (!this.firstVendorQuestionnaire) {
            return null;
        }

        return {
            total: this.firstVendorQuestionnaire.totalQuestions ?? 0,
            answered: this.firstVendorQuestionnaire.totalQuestionsAnswered ?? 0,
        };
    }

    get isDownloadingFile(): boolean {
        return this.getDownloadAnswerFileQuery.isLoading;
    }

    get isDeletingQuestionnaire(): boolean {
        return this.deleteQuestionnaireMutation.isPending || false;
    }

    get hasErrorDeletingQuestionnaire(): boolean {
        return this.deleteQuestionnaireMutation.hasError || false;
    }

    get errorDeletingQuestionnaire(): Error | null {
        return this.deleteQuestionnaireMutation.error;
    }

    get hasNextPage(): boolean {
        return this.getQuestionnaireAnswersQuery.hasNextPage;
    }

    get hasError(): boolean {
        return this.getQuestionnaireAnswersQuery.hasError;
    }

    deleteQuestionnaire = (questionnaireId: number, navigateTo?: string) => {
        if (this.isDeletingQuestionnaire) {
            return;
        }
        openConfirmationModal({
            title: t`Delete Questionnaire`,
            body: t`Are you sure you want to delete this questionnaire?`,
            confirmText: t`Yes, delete questionnaire`,
            cancelText: t`No, go back`,
            type: 'danger',
            size: 'md',
            onConfirm: () => {
                this.deleteQuestionnaireMutation
                    .mutateAsync({
                        path: { id: questionnaireId },
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'questionnaire-delete-success',
                            hasTimeout: true,
                            props: {
                                title: t`Questionnaire deleted successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                        if (navigateTo) {
                            sharedProgrammaticNavigationController.navigateTo(
                                navigateTo,
                            );
                        }
                    })
                    .catch((error) => {
                        snackbarController.addSnackbar({
                            id: 'questionnaire-delete-error',
                            props: {
                                title: t`Failed to delete questionnaire`,
                                description: isEmpty(error)
                                    ? t`Couldn't delete at the moment`
                                    : (error as Error).message,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    searchQuestionnaireAnswers = (search: string) => {
        this.currentQuery = search;
        this.lastSearchQuery = search;

        this.getQuestionnaireAnswersQuery.load({
            path: { id: Number(this.questionnaireId) },
            query: {
                page: DEFAULT_PAGE,
                limit: DEFAULT_LIMIT,
                q: search,
            },
        });
    };

    loadQuestionnaireAnswers = (query?: { q?: string }) => {
        if (!this.questionnaireId) {
            return;
        }

        // Update currentQuery for highlighting
        this.currentQuery = query?.q ?? this.lastSearchQuery;

        this.getQuestionnaireAnswersQuery.load({
            path: { id: Number(this.questionnaireId) },
            query: {
                page: DEFAULT_PAGE,
                limit: DEFAULT_LIMIT,
                q: this.currentQuery,
            },
        });
    };

    loadNextPage = ({ search }: { search?: string } = {}): void => {
        // Normalize search values - treat undefined and empty string as equivalent
        const normalizedSearch = search ?? '';
        const normalizedLastSearch = this.lastSearchQuery;

        if (normalizedSearch !== normalizedLastSearch) {
            this.getQuestionnaireAnswersQuery.unload();
            this.lastSearchQuery = normalizedSearch;
            this.loadQuestionnaireAnswers();

            return;
        }

        this.getQuestionnaireAnswersQuery.nextPage();
    };

    loadAll = (vendorId: number, questionnaireId: number) => {
        this.questionnaireId = questionnaireId;

        this.listVendorQuestionnairesQuery.load({
            path: { id: vendorId },
            query: {
                responseId: questionnaireId,
            },
        });
        this.loadQuestionnaireAnswers();
    };

    loadAndDownloadAnswerFile = async (options: {
        answerId: number;
        fileName: string;
    }) => {
        const { answerId, fileName } = options;

        this.getDownloadAnswerFileQuery.load({
            path: {
                id: answerId,
            },
        });

        await when(() => !this.getDownloadAnswerFileQuery.isLoading);

        const blob = this.getDownloadAnswerFileQuery.data;

        if (!blob) {
            return;
        }

        downloadBlob(blob, fileName);
    };
}

export const sharedVendorsQuestionnairesController =
    new VendorsQuestionnairesController();
