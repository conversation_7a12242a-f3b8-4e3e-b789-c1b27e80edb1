import { handleEditReviewDeadlineCloseModal } from '@components/vendors-security-reviews';
import { snackbarController } from '@controllers/snackbar';
import { vendorsSecurityReviewsControllerUpdateVendorSecurityReviewMutation } from '@globals/api-sdk/queries';
import type { VendorSecurityReviewRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedVendorsSecurityReviewDetailsController } from './vendors-security-review-details-controller';

class VendorsSecurityReviewDeadlineController {
    constructor() {
        makeAutoObservable(this);
    }

    updateReviewDeadlineMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerUpdateVendorSecurityReviewMutation,
    );

    get isUpdating(): boolean {
        return this.updateReviewDeadlineMutation.isPending;
    }

    updateReviewDeadline = (newDeadline: string): void => {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (
            !securityReviewDetails ||
            this.updateReviewDeadlineMutation.isPending
        ) {
            return;
        }

        const requestBody: VendorSecurityReviewRequestDto = {
            title: securityReviewDetails.title,
            reviewDeadlineAt: newDeadline,
            requestedAt: securityReviewDetails.requestedAt,
            securityReviewStatus: securityReviewDetails.status,
            securityReviewType: securityReviewDetails.type,
            requesterUserId: securityReviewDetails.requesterUser?.id,
        };

        this.updateReviewDeadlineMutation.mutate({
            path: { id: securityReviewDetails.id },
            body: requestBody,
        });

        when(
            () => !this.updateReviewDeadlineMutation.isPending,
            () => {
                const { hasError } = this.updateReviewDeadlineMutation;

                if (hasError) {
                    snackbarController.addSnackbar({
                        id: `update-review-deadline-error-${securityReviewDetails.id}`,
                        props: {
                            title: t`Failed to update review deadline`,
                            description: t`Please try again or contact support if the problem persists.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Success - close modal and show success message
                handleEditReviewDeadlineCloseModal();

                snackbarController.addSnackbar({
                    id: `update-review-deadline-success-${securityReviewDetails.id}`,
                    props: {
                        title: t`Review deadline updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Invalidate the security review details to refresh the data
                const { securityReviewDetailsQuery } =
                    sharedVendorsSecurityReviewDetailsController;

                securityReviewDetailsQuery.invalidate();
            },
        );
    };
}

export const sharedVendorsSecurityReviewDeadlineController =
    new VendorsSecurityReviewDeadlineController();
