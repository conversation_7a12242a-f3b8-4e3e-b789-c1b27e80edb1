import { isEmpty, uniqueId } from 'lodash-es';
import { sharedSOCReviewFinalizeReviewController } from '@components/vendors-security-reviews-soc';
import { snackbarController } from '@controllers/snackbar';
import {
    vendorsSecurityReviewsControllerDeleteSecurityReviewMutation,
    vendorsSecurityReviewsControllerGetVendorSecurityReviewSummaryOptions,
    vendorsSecurityReviewsControllerGetVendorsSecurityReviewsOptions,
    vendorsSecurityReviewsControllerUpdateVendorSecurityReviewStatusMutation,
} from '@globals/api-sdk/queries';
import type { VendorSecurityReviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { fileNameDate } from '@helpers/date-time';
import { downloadBlob } from '@helpers/download-file';
import { closeConfirmationModal } from '@helpers/temp-confirmation-modal';
import { sharedVendorsCurrentSecurityReviewsController } from './vendors-current-security-reviews-controller';
import { sharedVendorsSecurityReviewDetailsController } from './vendors-security-review-details-controller';
import { sharedVendorsSecurityReviewDocumentsController } from './vendors-security-review-documents-controller';
import { sharedVendorsSecurityReviewObservationsController } from './vendors-security-review-observations-controller';

class VendorsSecurityReviewMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    downloadingSecurityReviewId: number | null = null;

    deleteSecurityReviewMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerDeleteSecurityReviewMutation,
    );

    updateSecurityReviewStatusMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerUpdateVendorSecurityReviewStatusMutation,
    );

    securityReviewsQuery = new ObservedQuery(
        vendorsSecurityReviewsControllerGetVendorsSecurityReviewsOptions,
    );

    downloadSummaryQuery = new ObservedQuery(
        vendorsSecurityReviewsControllerGetVendorSecurityReviewSummaryOptions,
    );

    get isDownloadingSummary(): boolean {
        return this.downloadSummaryQuery.isLoading;
    }

    deleteSecurityReview = (id: number) => {
        if (!id || this.deleteSecurityReviewMutation.isPending) {
            return;
        }

        this.deleteSecurityReviewMutation.mutate({
            path: { id },
        });

        when(
            () => !this.deleteSecurityReviewMutation.isPending,
            () => {
                if (this.deleteSecurityReviewMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'security-review-delete-error',
                        props: {
                            title: 'Error deleting security review',
                            description:
                                'An error occurred while deleting the security review. Try again later.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        hasTimeout: true,
                        id: 'security-review-delete-success',
                        props: {
                            title: 'Security review deleted',
                            description:
                                'The security review was deleted successfully.',
                            severity: 'success',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }

                sharedVendorsCurrentSecurityReviewsController.paginatedSecurityReviews.invalidate();

                closeConfirmationModal();
            },
        );
    };

    deleteSecurityReviewWithCallback = (
        id: number,
        onSuccess?: () => void,
    ): void => {
        if (!id || this.deleteSecurityReviewMutation.isPending) {
            return;
        }

        this.deleteSecurityReviewMutation.mutate({
            path: { id },
        });

        when(
            () => !this.deleteSecurityReviewMutation.isPending,
            () => {
                if (this.deleteSecurityReviewMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'security-review-delete-error',
                        props: {
                            title: t`Error deleting security review`,
                            description: t`An error occurred while deleting the security review. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        hasTimeout: true,
                        id: 'security-review-delete-success',
                        props: {
                            title: t`Security review deleted`,
                            description: t`The security review was deleted successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    onSuccess?.();
                }

                sharedVendorsCurrentSecurityReviewsController.paginatedSecurityReviews.invalidate();

                closeConfirmationModal();
            },
        );
    };

    reopenSecurityReview = (
        vendorId: number,
        onSuccess?: (securityReviewId: number) => void,
    ): void => {
        if (!vendorId || this.updateSecurityReviewStatusMutation.isPending) {
            return;
        }

        // First, get the vendor's security reviews
        this.securityReviewsQuery.load({
            path: { vendorId },
            query: { sortDir: 'DESC' },
        });

        // Wait for the query to complete, then process
        when(
            () => !this.securityReviewsQuery.isLoading,
            () => {
                if (this.securityReviewsQuery.hasError) {
                    snackbarController.addSnackbar({
                        id: `reopen-review-error-${vendorId}`,
                        props: {
                            title: t`Failed to reopen review`,
                            description: t`There was an error reopening the security review. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const securityReviews = this.securityReviewsQuery.data?.data;

                if (!securityReviews || isEmpty(securityReviews)) {
                    snackbarController.addSnackbar({
                        id: `reopen-review-error-${vendorId}`,
                        props: {
                            title: t`Failed to reopen review`,
                            description: t`No security review found for this vendor.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Get the most recent security review (first in DESC order)
                const currentReview = securityReviews[0];

                // Update the security review status from COMPLETED to IN_PROGRESS
                this.updateSecurityReviewStatusMutation.mutate({
                    path: { id: currentReview.id },
                    body: {
                        status: 'IN_PROGRESS',
                        note: currentReview.note || '',
                        decision: currentReview.decision,
                    },
                });

                // Wait for the mutation to complete, then handle success/error
                when(
                    () => !this.updateSecurityReviewStatusMutation.isPending,
                    () => {
                        if (this.updateSecurityReviewStatusMutation.hasError) {
                            snackbarController.addSnackbar({
                                id: `reopen-review-error-${vendorId}`,
                                props: {
                                    title: t`Failed to reopen review`,
                                    description: t`There was an error reopening the security review. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        // Show success message
                        snackbarController.addSnackbar({
                            id: `reopen-review-success-${vendorId}`,
                            hasTimeout: true,
                            props: {
                                title: t`Security review reopened`,
                                description: t`The security review has been successfully reopened and is now in progress.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        // Invalidate the security reviews to refresh the data
                        sharedVendorsCurrentSecurityReviewsController.paginatedSecurityReviews.invalidate();

                        // Invalidate the security review details to update the status badge
                        sharedVendorsSecurityReviewDetailsController.securityReviewDetailsQuery.invalidate();

                        // Call the success callback for navigation
                        onSuccess?.(currentReview.id);
                    },
                );
            },
        );
    };

    reopenSpecificSecurityReview = (
        securityReviewId: number,
        onSuccess?: (securityReviewId: number, vendorId: number) => void,
    ): void => {
        if (
            !securityReviewId ||
            this.updateSecurityReviewStatusMutation.isPending
        ) {
            return;
        }

        // Get the security review details to get the vendor ID and current state
        const { loadSecurityReviewDetails, securityReviewDetailsQuery } =
            sharedVendorsSecurityReviewDetailsController;

        loadSecurityReviewDetails({
            path: { id: securityReviewId },
        });

        when(
            () => !securityReviewDetailsQuery.isLoading,
            () => {
                if (securityReviewDetailsQuery.hasError) {
                    snackbarController.addSnackbar({
                        id: `reopen-specific-review-error-${securityReviewId}`,
                        props: {
                            title: t`Failed to reopen review`,
                            description: t`There was an error reopening the security review. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const { securityReviewDetails } =
                    sharedVendorsSecurityReviewDetailsController;

                if (!securityReviewDetails) {
                    snackbarController.addSnackbar({
                        id: `reopen-specific-review-error-${securityReviewId}`,
                        props: {
                            title: t`Failed to reopen review`,
                            description: t`Security review not found.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Update the security review status from COMPLETED to IN_PROGRESS
                this.updateSecurityReviewStatusMutation.mutate({
                    path: { id: securityReviewId },
                    body: {
                        status: 'IN_PROGRESS',
                        note: securityReviewDetails.note || '',
                        decision: securityReviewDetails.decision,
                    },
                });

                when(
                    () => !this.updateSecurityReviewStatusMutation.isPending,
                    () => {
                        if (this.updateSecurityReviewStatusMutation.hasError) {
                            snackbarController.addSnackbar({
                                id: `reopen-specific-review-error-${securityReviewId}`,
                                props: {
                                    title: t`Failed to reopen review`,
                                    description: t`There was an error reopening the security review. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        snackbarController.addSnackbar({
                            id: `reopen-specific-review-success-${securityReviewId}`,
                            hasTimeout: true,
                            props: {
                                title: t`Security review reopened`,
                                description: t`The security review has been successfully reopened and is now in progress.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        sharedVendorsCurrentSecurityReviewsController.paginatedSecurityReviews.invalidate();

                        sharedVendorsSecurityReviewObservationsController.securityReviewObservationsQuery.invalidate();

                        securityReviewDetailsQuery.invalidate();

                        // Call the success callback for navigation
                        onSuccess?.(
                            securityReviewId,
                            securityReviewDetails.vendor?.id ?? 0,
                        );
                    },
                );
            },
        );
    };

    downloadSecurityReviewSummary = (securityReviewId: number): void => {
        if (!securityReviewId || this.downloadSummaryQuery.isLoading) {
            return;
        }

        this.downloadSummaryQuery.load({
            path: { id: securityReviewId },
        });

        when(
            () => !this.downloadSummaryQuery.isLoading,
            () => {
                const { data, hasError } = this.downloadSummaryQuery;

                if (hasError || !data) {
                    snackbarController.addSnackbar({
                        id: `download-summary-error-${uniqueId()}`,
                        props: {
                            title: t`Unable to download security review summary`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const timestamp = fileNameDate();
                const filename = `Security-Review-Summary-${timestamp}.pdf`;

                downloadBlob(data as Blob, filename);

                snackbarController.addSnackbar({
                    id: `download-summary-success-${uniqueId()}`,
                    hasTimeout: true,
                    props: {
                        title: t`Security review summary downloaded successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    downloadSecurityReview = async (
        original: VendorSecurityReviewResponseDto,
    ): Promise<void> => {
        if (this.downloadingSecurityReviewId !== null) {
            return;
        }

        this.downloadingSecurityReviewId = original.id;

        try {
            if (original.type === 'SOC_REPORT') {
                sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewDocuments(
                    {
                        path: { id: original.id },
                    },
                );

                await sharedSOCReviewFinalizeReviewController.downloadReportAsync(
                    original.id,
                    original.vendor?.id,
                );
            } else {
                this.downloadSecurityReviewSummary(original.id);

                await when(() => !this.downloadSummaryQuery.isLoading);
            }
        } finally {
            action(() => {
                this.downloadingSecurityReviewId = null;
            })();
        }
    };
}

export const sharedVendorsSecurityReviewMutationController =
    new VendorsSecurityReviewMutationController();
