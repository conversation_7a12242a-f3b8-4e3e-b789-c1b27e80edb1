import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    vendorsDocumentsControllerGetVendorDocumentsInfiniteOptions,
    vendorsDocumentsControllerGetVendorDocumentsOptions,
    vendorsQuestionnairesControllerListVendorQuestionnairesOptions,
} from '@globals/api-sdk/queries';
import type { DocumentResponseDto } from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedQuery,
} from '@globals/mobx';
import { sharedVendorsDetailsController } from './vendors-details-controller';

class VendorsProfileReportsAndDocumentsController {
    #lastSearchQuery = '';
    constructor() {
        makeAutoObservable(this);
    }

    paginatedQuestionnaires = new ObservedQuery(
        vendorsQuestionnairesControllerListVendorQuestionnairesOptions,
    );

    documentsInfiniteQuery = new ObservedInfiniteQuery(
        vendorsDocumentsControllerGetVendorDocumentsInfiniteOptions,
    );

    paginatedDocuments = new ObservedQuery(
        vendorsDocumentsControllerGetVendorDocumentsOptions,
    );

    loadPaginatedQuestionnaires = (params: FetchDataResponseParams) => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id) {
            return;
        }

        const { pagination } = params;
        const { page, pageSize } = pagination;

        const query: Required<
            Parameters<
                typeof vendorsQuestionnairesControllerListVendorQuestionnairesOptions
            >
        >[0]['query'] = {
            page,
            limit: pageSize,
        };

        this.paginatedQuestionnaires.load({
            query,
            path: { id: vendorDetails.id },
        });
    };

    get hasNextPage(): boolean {
        return this.documentsInfiniteQuery.hasNextPage;
    }

    get documentsList(): DocumentResponseDto[] {
        return (
            this.documentsInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get options(): ListBoxItemData[] {
        return this.documentsList.map(
            (document) =>
                ({
                    id: String(document.id),
                    label: document.name,
                    value: String(document.id),
                }) as ListBoxItemData,
        );
    }

    get isLoading(): boolean {
        return this.documentsInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.documentsInfiniteQuery.isFetching;
    }

    onFetchDocuments = ({
        search,
        increasePage,
        ...query
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextPage({ search });

            return;
        }

        this.loadDocuments({ ...query, q: search?.trim() });
    };

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.#lastSearchQuery) {
            this.documentsInfiniteQuery.unload();
            this.#lastSearchQuery = search ?? '';
            this.loadDocuments({ q: search });

            return;
        }

        this.documentsInfiniteQuery.nextPage();
    };

    loadDocuments = (query: { q?: string }) => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id) {
            return;
        }

        this.documentsInfiniteQuery.load({
            query: {
                ...query,
                limit: 20,
                onlyLibrary: true,
            },
            path: { id: vendorDetails.id },
        });
    };

    loadPaginatedDocuments = (params: FetchDataResponseParams) => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id) {
            return;
        }

        const { pagination } = params;
        const { page, pageSize } = pagination;

        const query: Required<
            Parameters<
                typeof vendorsDocumentsControllerGetVendorDocumentsOptions
            >
        >[0]['query'] = {
            page,
            limit: pageSize,
            onlyLibrary: true,
        };

        this.paginatedDocuments.load({
            query,
            path: { id: vendorDetails.id },
        });
    };
}

export const sharedVendorsProfileReportsAndDocumentsController =
    new VendorsProfileReportsAndDocumentsController();
