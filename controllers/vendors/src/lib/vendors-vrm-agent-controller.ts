import { isEmpty } from 'lodash-es';
import {
    type UtilitiesVrmAgentMessageAction,
    type UtilitiesVrmAgentMessageData,
    VRM_AGENT_MOCK_MESSAGES,
} from '@components/utilities';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { VRM_AGENT_REFERENCE_MAPPING } from './constants/vrm-agent-reference-mapping.constants';
import type { VrmAgentReferenceId } from './types/vendor-vrm-agent.types';
import { sharedVendorVrmAgentActionsController } from './vendor-vrm-agent-actions.controller';

class VendorsVrmAgentController {
    currentVendorId: string | null = null;

    #actionsController = sharedVendorVrmAgentActionsController;

    constructor() {
        makeAutoObservable(this);
    }

    mockMessages: UtilitiesVrmAgentMessageData[] = [];
    isLoadingMock = false;
    isExecutingActionState = false;

    get workflowMessages(): UtilitiesVrmAgentMessageData[] {
        return this.mockMessages;
    }

    get isLoading(): boolean {
        return this.isLoadingMock;
    }

    get hasWorkflowMessages(): boolean {
        return !isEmpty(this.workflowMessages);
    }

    get isExecutingAction(): boolean {
        return this.isExecutingActionState;
    }

    load = (vendorId: string): void => {
        this.currentVendorId = vendorId;

        this.loadMockData();
    };

    handleActionClick = (action: UtilitiesVrmAgentMessageAction): void => {
        switch (action.type) {
            case 'button':
            case 'action': {
                this.executeAction(action.action);
                break;
            }
            case 'link': {
                break;
            }
            default: {
                console.warn(`Unknown action type: ${action.type}`);
                break;
            }
        }
    };

    executeAction = (actionId: string): void => {
        if (!this.currentVendorId) {
            console.warn('Cannot execute action: no vendor ID set');

            return;
        }

        this.isExecutingActionState = true;

        try {
            this.#actionsController.executeAction(
                actionId,
                this.currentVendorId,
            );
        } catch (error) {
            console.error('Failed to execute action:', error);
        } finally {
            this.isExecutingActionState = false;
        }
    };

    resolveReferenceToUrl = (refId: string): string | null => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace || !this.currentVendorId) {
            return null;
        }

        if (!(refId in VRM_AGENT_REFERENCE_MAPPING)) {
            return null;
        }

        const routePattern =
            VRM_AGENT_REFERENCE_MAPPING[refId as VrmAgentReferenceId];

        const route = routePattern.replace(':vendorId', this.currentVendorId);

        return `/workspaces/${currentWorkspace.id}/${route}`;
    };

    processItemsWithReferences = <
        T extends { ref?: string } | { action: string; type: string },
    >(
        items: T[],
    ): T[] => {
        return items.map((item) => {
            // Handle actions with type 'link'
            if ('action' in item && 'type' in item && item.type === 'link') {
                const resolvedUrl = this.resolveReferenceToUrl(item.action);

                return {
                    ...item,
                    action: resolvedUrl || item.action,
                };
            }

            // Handle items with ref property
            if ('ref' in item && item.ref) {
                const resolvedUrl = this.resolveReferenceToUrl(item.ref);

                return {
                    ...item,
                    ref: resolvedUrl || item.ref,
                };
            }

            return item;
        });
    };

    loadMockData = (): void => {
        this.isLoadingMock = true;
        const rawMessages = VRM_AGENT_MOCK_MESSAGES;

        this.mockMessages = rawMessages.map((message) => ({
            ...message,
            title: message.title
                ? this.processItemsWithReferences(message.title)
                : message.title,
            body: message.body
                ? this.processItemsWithReferences(message.body)
                : message.body,
            actions: this.processItemsWithReferences(message.actions ?? []),
        }));

        this.isLoadingMock = false;
    };
}

export const sharedVendorsVrmAgentController = new VendorsVrmAgentController();
