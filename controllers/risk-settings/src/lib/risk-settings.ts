import {
    riskManagementControllerGetRiskSettingsOptions,
    riskManagementControllerUpdateRiskSettingsMutation,
} from '@globals/api-sdk/queries';
import type {
    RiskSettingsRequestDto,
    RiskSettingsResponseDto,
} from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';

class RiskSettingsController {
    constructor() {
        makeAutoObservable(this);
    }

    riskSettingsQuery = new ObservedQuery(
        riskManagementControllerGetRiskSettingsOptions,
    );

    updateRiskSettingsMutation = new ObservedMutation(
        riskManagementControllerUpdateRiskSettingsMutation,
        {
            onSuccess: () => {
                // Invalidate the settings query to refresh data
                this.riskSettingsQuery.invalidate();
            },
        },
    );

    get isLoading(): boolean {
        return this.riskSettingsQuery.isLoading;
    }

    get isUpdating(): boolean {
        return this.updateRiskSettingsMutation.isPending;
    }

    get riskSettings(): RiskSettingsResponseDto | null {
        return this.riskSettingsQuery.data;
    }

    get hasError(): Error | null {
        return this.updateRiskSettingsMutation.error;
    }

    load = (): void => {
        this.riskSettingsQuery.load();
    };

    updateRiskSettings = (data: RiskSettingsRequestDto): void => {
        this.updateRiskSettingsMutation.mutate({
            body: data,
        });
    };
}

export const sharedRiskSettingsController = new RiskSettingsController();
