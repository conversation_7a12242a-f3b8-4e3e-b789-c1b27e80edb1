import { vendorsRiskManagementControllerGetVendorsRisksStatusStatsOptions } from '@globals/api-sdk/queries';
import type { VendorsRisksStatusStatsResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class DashboardVendorRisksController {
    constructor() {
        makeAutoObservable(this);
    }

    #vendorStatsQuery = new ObservedQuery(
        vendorsRiskManagementControllerGetVendorsRisksStatusStatsOptions,
    );

    get vendorRiskStats(): VendorsRisksStatusStatsResponseDto | null {
        return this.#vendorStatsQuery.data;
    }

    get isLoading(): boolean {
        return this.#vendorStatsQuery.isLoading;
    }

    loadVendorRisksOverview = () => {
        this.#vendorStatsQuery.load();
    };
}

export const sharedDashboardVendorRisksController =
    new DashboardVendorRisksController();
