import { isEmpty, isNil } from 'lodash-es';
import {
    getEvidenceTypeApiValue,
    sharedAuditHubEvidenceController,
} from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import {
    auditHubControllerGetAuditEvidenceDownloadOptions,
    customerRequestControllerCreateAuditorCustomerRequestMessageMutation,
    customerRequestControllerDeleteCustomerRequestMessageMutation,
    customerRequestControllerGetCustomerRequestFileSignedUrlOptions,
    customerRequestControllerGetCustomerRequestMessagesOptions,
    customerRequestControllerUpdateCustomerRequestMessageMutation,
    customerRequestControllerUpdateCustomerRequestNotificationsMutation,
} from '@globals/api-sdk/queries';
import type {
    AuditHubEvidenceResponseDto,
    AuditHubEvidenceTypeEnum,
    NoteResponseDto,
    UserCardResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import {
    downloadBase64AsFile,
    downloadFileFromSignedUrl,
} from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { ReferenceDocument } from '../../../../components/utilities/src/lib/notes/comments/utilities-notes-comment-types';
import type { NoteUpdateDto } from '../../../../components/utilities/src/lib/notes/utilities-notes-create-dto-types';
import { UtilitiesBase, type UtilitiesBaseConfig } from './utilities-base';

interface Message {
    'files[]'?: (Blob | File)[];
    fileMetadata?: {
        originalFile?: string;
        name?: string;
        creationDate?: string;
    }[];
    comment?: string;
    hasReference?: boolean;
    documentActive?: {
        name?: string;
        type?: string;
    } | null;
}

export interface RawCustomerRequestMessage {
    id?: number;
    message?: string;
    sentAt?: string;
    hasBeenRead?: boolean;
    authorId?: number;
    author?: string;
    avatar?: string;
    authorIsAuditor?: boolean;
    files?: {
        id?: number;
        name?: string;
        url?: string;
    }[];
    referenceDocuments?: ReferenceDocument[];
}

export interface CustomerRequestUtilitiesNotesConfig
    extends UtilitiesBaseConfig {
    overrides?: {
        enabled?: boolean;
    };
}

export class CustomerRequestUtilitiesNotesController extends UtilitiesBase<CustomerRequestUtilitiesNotesConfig> {
    customerRequestMessagesQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestMessagesOptions,
    );

    createMessageMutation = new ObservedMutation(
        customerRequestControllerCreateAuditorCustomerRequestMessageMutation,
    );

    updateMessageMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestMessageMutation,
    );

    deleteMessageMutation = new ObservedMutation(
        customerRequestControllerDeleteCustomerRequestMessageMutation,
    );

    updateNotificationsMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestNotificationsMutation,
    );

    downloadAttachmentQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestFileSignedUrlOptions,
    );

    requestId: number | null = null;
    clientId: string | null = null;

    constructor() {
        super();
        makeObservable(this);
    }

    get messages(): NoteResponseDto[] {
        const rawMessages =
            this.customerRequestMessagesQuery.data?.messages ?? [];

        return rawMessages.map((message: RawCustomerRequestMessage) => ({
            id: String(message.id),
            comment: message.message,
            createdAt: message.sentAt,
            updatedAt: message.sentAt,
            hasBeenRead: message.hasBeenRead ?? false,
            owner: {
                entryId: message.authorId ?? '', // Use authorId as entryId for proper comparison
                firstName: message.author ?? '',
                jobTitle: null,
                avatarUrl: message.avatar,
                drataTermsAgreedAt: null,
                createdAt: message.sentAt,
                updatedAt: message.sentAt,
                authorIsAuditor: message.authorIsAuditor ?? false,
            } as UserCardResponseDto & { authorIsAuditor: boolean },
            noteFiles: (message.files ?? []).map((file, index: number) => ({
                id: String(file.id ?? index),
                name: file.name ?? `file-${index}`,
                file: file.url,
                createdAt: message.sentAt,
                updatedAt: message.sentAt,
            })),
            referenceDocuments: message.referenceDocuments ?? [],
        })) as NoteResponseDto[];
    }

    get isLoading(): boolean {
        return this.customerRequestMessagesQuery.isLoading;
    }

    loadMessages = (requestId: number, clientId: string): void => {
        if (isNil(requestId) || isNil(clientId)) {
            throw new Error(t`Request ID and Client ID are required`);
        }

        this.requestId = requestId;
        this.clientId = clientId;

        this.customerRequestMessagesQuery.load({
            path: {
                customerRequestId: requestId,
            },
        });
    };

    createNote = (values: Message): void => {
        const { comment } = values;

        if (!this.requestId) {
            throw new Error(t`Request ID is required`);
        }

        if (!comment) {
            return;
        }

        const timestamp = new Date().toISOString();

        // Prepare the request body with attachments if they exist
        const body: {
            message?: string;
            'files[]'?: File[];
            'referenceDocuments[]'?: string[];
        } = {};

        if (comment.trim()) {
            body.message = comment;
        }

        if (values['files[]'] && !isEmpty(values['files[]'])) {
            body['files[]'] = values['files[]'] as File[];
        }

        // Add reference documents if hasReference is true and documentActive exists
        const { hasReference, documentActive } = values;

        if (hasReference && documentActive) {
            const { type, name } = documentActive;

            body['referenceDocuments[]'] = [
                JSON.stringify({
                    messageId: this.requestId,
                    documentType: type || '',
                    documentName: name || '',
                }),
            ];
        } else if (hasReference === false) {
            // Send a special placeholder to indicate clearing reference documents
            body['referenceDocuments[]'] = [''];
        }

        this.createMessageMutation
            .mutateAsync({
                path: {
                    customerRequestId: this.requestId,
                    clientId: this.clientId || '',
                },
                body,
            })
            .then(() => {
                this.customerRequestMessagesQuery.invalidate();
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-customer-request-message-success`,
                    props: {
                        title: t`Message sent`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-customer-request-message-error`,
                    props: {
                        title: t`Unable to send message`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    updateNote = (messageId: string, values: NoteUpdateDto): void => {
        const { comment, filesToDelete, hasReference, documentActive } = values;
        const timestamp = new Date().toISOString();

        // Prepare the request body
        const body: {
            message?: string;
            filesToDelete?: number[];
            'files[]'?: File[];
            'referenceDocuments[]'?: string[];
        } = {};

        if (comment.trim()) {
            body.message = comment;
        }

        if (filesToDelete) {
            body.filesToDelete = filesToDelete.map(Number);
        }

        if (values['files[]']) {
            body['files[]'] = values['files[]'] as File[];
        }

        // Handle reference documents if hasReference is present in values
        if (hasReference) {
            const rawMessages =
                this.customerRequestMessagesQuery.data?.messages ?? [];
            const existingRawMessage = rawMessages.find(
                (msg: RawCustomerRequestMessage) =>
                    String(msg.id) === messageId,
            );
            const referenceDocuments = existingRawMessage?.referenceDocuments as
                | ReferenceDocument[]
                | undefined;
            const savedReferenceDocument =
                referenceDocuments && !isEmpty(referenceDocuments)
                    ? referenceDocuments[0]
                    : undefined;

            if (savedReferenceDocument) {
                body['referenceDocuments[]'] = [
                    JSON.stringify({
                        messageId: this.requestId,
                        documentType: savedReferenceDocument.documentType || '',
                        documentName: savedReferenceDocument.documentName || '',
                    }),
                ];
            } else if (documentActive) {
                body['referenceDocuments[]'] = [
                    JSON.stringify({
                        messageId: this.requestId,
                        documentType: documentActive.type || '',
                        documentName: documentActive.name || '',
                    }),
                ];
            }
        } else if (hasReference === false) {
            // Send a special placeholder to indicate clearing reference documents
            body['referenceDocuments[]'] = [''];
        }

        this.updateMessageMutation
            .mutateAsync({
                path: {
                    messageId: Number(messageId),
                },
                body,
            })
            .then(() => {
                this.customerRequestMessagesQuery.invalidate();
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-customer-request-message-success`,
                    props: {
                        title: t`Message updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-customer-request-message-error`,
                    props: {
                        title: t`Unable to update message`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    updateMessageReadStatus = (
        messageId: string,
        hasBeenRead: boolean,
    ): void => {
        if (!this.requestId) {
            throw new Error(t`Request ID is required`);
        }

        const timestamp = new Date().toISOString();

        this.updateNotificationsMutation
            .mutateAsync({
                path: {
                    customerRequestId: this.requestId,
                    messageId: Number(messageId),
                },
            })
            .then(() => {
                this.customerRequestMessagesQuery.invalidate();
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-message-read-status-success`,
                    props: {
                        title: hasBeenRead
                            ? t`Message marked as read`
                            : t`Message marked as unread`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-message-read-status-error`,
                    props: {
                        title: t`Unable to update message status`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    deleteNote = (messageId: string): void => {
        const timestamp = new Date().toISOString();

        openConfirmationModal({
            title: t`Delete message?`,
            body: t`If you delete this message? This action cannot be undone.`,
            confirmText: t`Yes, delete message`,
            cancelText: t`No, go back`,
            type: 'danger',
            onConfirm: () => {
                this.deleteMessageMutation
                    .mutateAsync({
                        path: {
                            messageId: Number(messageId),
                        },
                    })
                    .then(() => {
                        this.customerRequestMessagesQuery.invalidate();
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-customer-request-message-success`,
                            props: {
                                title: t`Message deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-customer-request-message-error`,
                            props: {
                                title: t`Unable to delete message`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    downloadNoteAttachment = (noteFileId: string): void => {
        this.downloadAttachmentQuery.load({
            path: { fileId: Number(noteFileId) },
        });
        when(() => !this.downloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.downloadAttachmentQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    handleSearchAndDownloadDocument = (refDoc: ReferenceDocument): void => {
        if (!this.validateRequiredIds()) {
            return;
        }

        this.searchForDocument(refDoc);
        this.waitForSearchResultsAndDownload(refDoc);
    };

    validateRequiredIds = (): boolean => {
        const { getRequestId: requestId, auditorFrameworkId: auditId } =
            sharedCustomerRequestDetailsController;

        return Boolean(auditId && requestId);
    };

    searchForDocument = (refDoc: ReferenceDocument): void => {
        const { getRequestId: requestId, auditorFrameworkId: auditId } =
            sharedCustomerRequestDetailsController;

        sharedAuditHubEvidenceController.auditCustomerRequestEvidencesQuery.load(
            {
                query: {
                    page: DEFAULT_PAGE,
                    limit: DEFAULT_PAGE_SIZE,
                    q: refDoc.documentName,
                },
                path: {
                    auditId: String(auditId),
                    customerRequestId: Number(requestId),
                },
            },
        );
    };

    waitForSearchResultsAndDownload = (refDoc: ReferenceDocument): void => {
        when(
            () =>
                !sharedAuditHubEvidenceController.auditCustomerRequestEvidencesIsLoading,
            () => {
                this.processSearchResultsAndDownload(refDoc);
            },
        );
    };

    processSearchResultsAndDownload = (refDoc: ReferenceDocument): void => {
        const result =
            sharedAuditHubEvidenceController.auditCustomerRequestEvidences;

        if (isEmpty(result)) {
            return;
        }

        const matchingDocument = this.findMatchingDocument(result, refDoc);

        if (!matchingDocument) {
            return;
        }

        this.downloadDocument(matchingDocument, refDoc.documentName);
    };

    findMatchingDocument(
        documents: AuditHubEvidenceResponseDto[],
        refDoc: ReferenceDocument,
    ): AuditHubEvidenceResponseDto | null {
        return (
            documents.find(
                (doc) =>
                    doc.name === refDoc.documentName &&
                    doc.type === refDoc.documentType,
            ) ?? null
        );
    }

    downloadDocument = (
        document: AuditHubEvidenceResponseDto,
        documentName: string,
    ): void => {
        // Download using signedUrl if available
        if (document.signedUrl) {
            downloadFileFromSignedUrl(document.signedUrl);

            return;
        }

        // Download using fileData if available
        if (document.fileData && document.fileName) {
            const mimeType = document.fileType || 'application/octet-stream';

            downloadBase64AsFile(
                document.fileData,
                document.fileName,
                mimeType,
            );

            return;
        }

        // Generate evidence for the document since no direct download is available
        this.generateAndDownloadEvidence(document, documentName);
    };

    generateAndDownloadEvidence = (
        evidence: { type?: string; artifact?: string; controlCodes?: string[] },
        documentName: string,
    ): void => {
        const { getRequestId: requestId, auditorFrameworkId: auditId } =
            sharedCustomerRequestDetailsController;

        if (!auditId || !requestId) {
            const timestamp = new Date().toISOString();

            snackbarController.addSnackbar({
                id: `${timestamp}-evidence-generation-missing-params`,
                props: {
                    title: t`Unable to generate evidence`,
                    description: t`Missing required audit or request information. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const downloadQuery = new ObservedQuery(
            auditHubControllerGetAuditEvidenceDownloadOptions,
        );

        if (
            !evidence.type ||
            !evidence.artifact ||
            !evidence.controlCodes ||
            isEmpty(evidence.controlCodes)
        ) {
            const timestamp = new Date().toISOString();

            snackbarController.addSnackbar({
                id: `${timestamp}-evidence-generation-missing-evidence-params`,
                props: {
                    title: t`Unable to generate evidence`,
                    description: t`Missing required evidence information (type, artifact, or control codes) for ${documentName}.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const queryParams = {
            type: getEvidenceTypeApiValue(
                evidence.type as AuditHubEvidenceTypeEnum,
            ) as AuditHubEvidenceTypeEnum,
            artifact: evidence.artifact,
            controlCode: evidence.controlCodes[0],
        };

        downloadQuery.load({
            path: {
                auditId: String(auditId),
                customerRequestId: Number(requestId),
            },
            query: queryParams,
        });

        when(
            () => !downloadQuery.isLoading,
            () => {
                const timestamp = new Date().toISOString();

                if (downloadQuery.error) {
                    snackbarController.addSnackbar({
                        id: `${timestamp}-evidence-generation-error`,
                        props: {
                            title: t`Unable to generate evidence`,
                            description: t`Failed to generate evidence for ${documentName}. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const { data } = downloadQuery;
                const { signedUrl, fileData, fileName, fileType } = data ?? {};

                // Handle the download based on what's available in the response
                if (signedUrl) {
                    downloadFileFromSignedUrl(signedUrl);
                } else if (fileData && fileName) {
                    const mimeType = fileType || 'application/octet-stream';

                    downloadBase64AsFile(fileData, fileName, mimeType);
                } else {
                    snackbarController.addSnackbar({
                        id: `${timestamp}-evidence-generation-no-data`,
                        props: {
                            title: t`No evidence data available`,
                            description: t`Unable to download evidence for ${documentName}. No download data was returned.`,
                            severity: 'warning',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };
}

export const sharedCustomerRequestUtilitiesNotesController =
    new CustomerRequestUtilitiesNotesController();
